<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TikTok Clone</title>
  <link rel="icon" href="tiktok-icon.svg">
  <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/babel-standalone@6/babel.min.js"></script>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    :root {
      --primary-color: #fe2c55;
      --secondary-color: #25f4ee;
      --text-color: #161823;
      --white: #ffffff;
      --gray: #f1f1f2;
      --light-gray: #e3e3e4;
      --dark-gray: #8a8a8a;
      --black: #000000;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: var(--white);
      color: var(--text-color);
    }
    
    .splash-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: var(--black);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      opacity: 1;
      transition: opacity 0.5s ease-out;
    }
    
    .splash-screen.fade-out {
      opacity: 0;
      pointer-events: none;
    }
    
    .splash-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    
    .logo-container {
      margin-bottom: 20px;
    }
    
    .tiktok-logo {
      position: relative;
      width: 80px;
      height: 80px;
      background-color: transparent;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .note-1, .note-2 {
      position: absolute;
      width: 45px;
      height: 80px;
      background-color: transparent;
      border-radius: 20px;
    }
    
    .note-1 {
      left: 0;
      background-color: var(--secondary-color);
      transform: skew(20deg);
      z-index: 1;
    }
    
    .note-2 {
      right: 0;
      background-color: var(--primary-color);
      transform: skew(-20deg);
      z-index: 2;
    }
    
    .note-1::before, .note-2::before {
      content: '';
      position: absolute;
      top: 10px;
      width: 10px;
      height: 10px;
      background-color: var(--white);
      border-radius: 50%;
    }
    
    .note-1::before {
      right: 10px;
    }
    
    .note-2::before {
      left: 10px;
    }
    
    .app-name {
      color: var(--white);
      font-size: 2rem;
      font-weight: bold;
      margin-top: 20px;
    }
    
    .app-container {
      max-width: 100vw;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .navbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 60px;
      padding: 0 20px;
      border-bottom: 1px solid var(--light-gray);
      background-color: var(--white);
      position: sticky;
      top: 0;
      z-index: 100;
    }
    
    .navbar-left .logo {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 1.5rem;
      font-weight: bold;
      color: var(--primary-color);
      text-decoration: none;
    }
    
    .tiktok-logo-small {
      position: relative;
      width: 30px;
      height: 30px;
      background-color: transparent;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .note-1-small, .note-2-small {
      position: absolute;
      width: 16px;
      height: 30px;
      background-color: transparent;
      border-radius: 8px;
    }
    
    .note-1-small {
      left: 0;
      background-color: var(--secondary-color);
      transform: skew(20deg);
      z-index: 1;
    }
    
    .note-2-small {
      right: 0;
      background-color: var(--primary-color);
      transform: skew(-20deg);
      z-index: 2;
    }
    
    .note-1-small::before, .note-2-small::before {
      content: '';
      position: absolute;
      top: 5px;
      width: 4px;
      height: 4px;
      background-color: var(--white);
      border-radius: 50%;
    }
    
    .note-1-small::before {
      right: 4px;
    }
    
    .note-2-small::before {
      left: 4px;
    }
    
    .navbar-center {
      flex: 1;
      max-width: 500px;
      margin: 0 20px;
    }
    
    .search-form {
      display: flex;
      align-items: center;
      background-color: var(--gray);
      border-radius: 92px;
      padding: 0 16px;
      height: 40px;
      position: relative;
    }
    
    .search-form input {
      flex: 1;
      border: none;
      background: transparent;
      padding: 0 10px;
      font-size: 0.9rem;
      outline: none;
    }
    
    .search-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      border: none;
      background: transparent;
      cursor: pointer;
      color: var(--dark-gray);
    }
    
    .navbar-right {
      display: flex;
      align-items: center;
      gap: 16px;
    }
    
    .upload-btn, .profile-btn, .login-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
      border-radius: 4px;
      font-weight: 600;
      font-size: 0.9rem;
      text-decoration: none;
    }
    
    .upload-btn {
      border: 1px solid var(--light-gray);
      color: var(--text-color);
    }
    
    .login-btn {
      background-color: var(--primary-color);
      color: var(--white);
      border: none;
      cursor: pointer;
    }
    
    .main-container {
      display: flex;
      height: calc(100vh - 60px);
    }
    
    .sidebar {
      width: 300px;
      height: 100%;
      border-right: 1px solid var(--light-gray);
      padding: 20px 0;
      overflow-y: auto;
    }
    
    .sidebar-menu {
      margin-bottom: 20px;
    }
    
    .sidebar-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px 20px;
      font-size: 1rem;
      color: var(--text-color);
      transition: background-color 0.2s;
      text-decoration: none;
    }
    
    .sidebar-item:hover {
      background-color: var(--gray);
    }
    
    .sidebar-item.active {
      color: var(--primary-color);
    }
    
    .content {
      flex: 1;
      overflow-y: auto;
      padding: 20px;
      height: 100%;
    }
    
    .home-container {
      width: 100%;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .video-feed {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    
    .video-card {
      display: flex;
      flex-direction: column;
      padding: 20px 0;
      border-bottom: 1px solid var(--light-gray);
      max-width: 600px;
      margin: 0 auto;
    }
    
    .video-card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .video-user {
      display: flex;
      align-items: center;
      gap: 12px;
      text-decoration: none;
      color: var(--text-color);
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
    }
    
    .user-info {
      display: flex;
      flex-direction: column;
    }
    
    .username {
      font-weight: 600;
      font-size: 1rem;
    }
    
    .name {
      font-size: 0.9rem;
      color: var(--dark-gray);
    }
    
    .follow-btn {
      padding: 6px 16px;
      border: 1px solid var(--primary-color);
      color: var(--primary-color);
      border-radius: 4px;
      font-weight: 600;
      font-size: 0.9rem;
      background: transparent;
      cursor: pointer;
      transition: background-color 0.2s;
    }
    
    .follow-btn:hover {
      background-color: rgba(254, 44, 85, 0.06);
    }
    
    .video-caption {
      margin-bottom: 15px;
    }
    
    .video-caption p {
      margin-bottom: 5px;
    }
    
    .video-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      margin-bottom: 5px;
    }
    
    .tag {
      color: var(--primary-color);
      font-weight: 600;
      cursor: pointer;
    }
    
    .video-music {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 0.9rem;
      color: var(--dark-gray);
    }
    
    .video-container {
      position: relative;
      display: flex;
      border-radius: 8px;
      overflow: hidden;
    }
    
    .video-player {
      width: 100%;
      max-height: 80vh;
      object-fit: contain;
      background-color: black;
      cursor: pointer;
    }
    
    .video-actions {
      position: absolute;
      right: 15px;
      bottom: 15px;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }
    
    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 5px;
      color: var(--white);
      background: rgba(22, 24, 35, 0.34);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      justify-content: center;
      border: none;
      cursor: pointer;
    }
    
    .action-btn span {
      font-size: 0.8rem;
    }
    
    .action-btn.liked {
      color: var(--primary-color);
    }
    
    /* For mobile devices */
    @media (max-width: 768px) {
      .navbar-center {
        display: none;
      }
      
      .upload-btn span, .login-btn span {
        display: none;
      }
      
      .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--light-gray);
        padding: 10px 0;
      }
      
      .sidebar-menu {
        display: flex;
        justify-content: space-around;
        margin-bottom: 0;
      }
      
      .sidebar-item {
        flex-direction: column;
        padding: 10px;
        font-size: 0.8rem;
      }
      
      .main-container {
        flex-direction: column;
      }
      
      .video-card {
        padding: 15px 0;
      }
      
      .video-player {
        max-height: 70vh;
      }
    }
  </style>
</head>
<body>
  <div id="root"></div>

  <script type="text/babel">
    // SplashScreen Component
    function SplashScreen({ onFinish }) {
      React.useEffect(() => {
        const timer = setTimeout(() => {
          onFinish();
        }, 2500);
        
        return () => clearTimeout(timer);
      }, [onFinish]);
      
      return (
        <div className="splash-screen">
          <div className="splash-content">
            <div className="logo-container">
              <div className="tiktok-logo">
                <div className="note-1"></div>
                <div className="note-2"></div>
              </div>
            </div>
            <h1 className="app-name">TikTok Clone</h1>
          </div>
        </div>
      );
    }
    
    // Navbar Component
    function Navbar() {
      return (
        <nav className="navbar">
          <div className="navbar-left">
            <a href="#" className="logo">
              <div className="tiktok-logo-small">
                <div className="note-1-small"></div>
                <div className="note-2-small"></div>
              </div>
              <span>TikTok Clone</span>
            </a>
          </div>
          
          <div className="navbar-center">
            <div className="search-form">
              <input type="text" placeholder="Search accounts and videos" />
              <button className="search-btn">🔍</button>
            </div>
          </div>
          
          <div className="navbar-right">
            <a href="#" className="upload-btn">
              <span>+</span>
              <span>Upload</span>
            </a>
            
            <button className="login-btn">
              <span>Log In</span>
            </button>
          </div>
        </nav>
      );
    }
    
    // Sidebar Component
    function Sidebar() {
      return (
        <div className="sidebar">
          <div className="sidebar-menu">
            <a href="#" className="sidebar-item active">
              <span>🏠</span>
              <span>For You</span>
            </a>
            <a href="#" className="sidebar-item">
              <span>👥</span>
              <span>Following</span>
            </a>
            <a href="#" className="sidebar-item">
              <span>🔍</span>
              <span>Explore</span>
            </a>
            <a href="#" className="sidebar-item">
              <span>📹</span>
              <span>LIVE</span>
            </a>
          </div>
        </div>
      );
    }
    
    // VideoCard Component
    function VideoCard({ video }) {
      return (
        <div className="video-card">
          <div className="video-card-header">
            <a href="#" className="video-user">
              <img src={video.userAvatar} alt={video.username} className="user-avatar" />
              <div className="user-info">
                <span className="username">@{video.username}</span>
                <span className="name">{video.name}</span>
              </div>
            </a>
            <button className="follow-btn">Follow</button>
          </div>
          
          <div className="video-caption">
            <p>{video.caption}</p>
            {video.tags && (
              <div className="video-tags">
                {video.tags.map((tag, index) => (
                  <span key={index} className="tag">#{tag}</span>
                ))}
              </div>
            )}
            {video.music && (
              <div className="video-music">
                <span>🎵</span>
                <span>{video.music}</span>
              </div>
            )}
          </div>
          
          <div className="video-container">
            <video
              src={video.videoUrl}
              className="video-player"
              controls
              loop
            />
            
            <div className="video-actions">
              <button className="action-btn">
                <span>❤️</span>
                <span>{video.likes}</span>
              </button>
              <button className="action-btn">
                <span>💬</span>
                <span>{video.comments}</span>
              </button>
              <button className="action-btn">
                <span>↗️</span>
                <span>{video.shares}</span>
              </button>
            </div>
          </div>
        </div>
      );
    }
    
    // Home Component
    function Home() {
      // Mock video data
      const videos = [
        {
          id: 1,
          username: 'user1',
          name: 'User One',
          userAvatar: 'https://placehold.co/50',
          caption: 'This is my first TikTok video! #firstpost #tiktok',
          tags: ['firstpost', 'tiktok'],
          music: 'Original Sound - user1',
          videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-young-woman-waving-her-hair-in-a-pool-1229-large.mp4',
          likes: 1234,
          comments: 123,
          shares: 45
        },
        {
          id: 2,
          username: 'user2',
          name: 'User Two',
          userAvatar: 'https://placehold.co/50',
          caption: 'Check out this cool dance! #dance #viral',
          tags: ['dance', 'viral'],
          music: 'Popular Song - Famous Artist',
          videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-man-dancing-under-changing-lights-1240-large.mp4',
          likes: 5678,
          comments: 456,
          shares: 78
        }
      ];
      
      return (
        <div className="home-container">
          <div className="video-feed">
            {videos.map(video => (
              <VideoCard key={video.id} video={video} />
            ))}
          </div>
        </div>
      );
    }
    
    // Main App Component
    function App() {
      const [showSplash, setShowSplash] = React.useState(true);
      
      const handleSplashFinish = () => {
        setShowSplash(false);
      };
      
      return (
        <React.Fragment>
          {showSplash ? (
            <SplashScreen onFinish={handleSplashFinish} />
          ) : (
            <div className="app-container">
              <Navbar />
              <div className="main-container">
                <Sidebar />
                <div className="content">
                  <Home />
                </div>
              </div>
            </div>
          )}
        </React.Fragment>
      );
    }
    
    // Render the App
    const root = ReactDOM.createRoot(document.getElementById('root'));
    root.render(<App />);
  </script>
</body>
</html>
