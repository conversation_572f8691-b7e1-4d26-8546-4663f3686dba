.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  position: relative;
}

.profile-cover {
  height: 300px;
  width: 100%;
  background-size: cover;
  background-position: center;
  position: relative;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));
}

.profile-header {
  display: flex;
  justify-content: space-between;
  padding: 20px 40px;
  margin-bottom: 20px;
  position: relative;
}

.profile-info {
  display: flex;
  gap: 30px;
}

.profile-avatar-container {
  margin-top: -50px;
  position: relative;
  z-index: 2;
}

.profile-avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 5px solid var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dark .profile-avatar {
  border-color: #1e1e1e;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding-top: 20px;
}

.profile-name-section {
  margin-bottom: 5px;
}

.username-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.verified-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #6C13B3;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
}

.dark .verified-badge {
  background-color: #9B4BDE;
}

.user-badges {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.user-badge {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--gray);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.dark .social-link {
  background-color: #2a2a2a;
  color: var(--white);
}

.social-link:hover {
  transform: translateY(-3px);
  background-color: #6C13B3;
  color: white;
}

.dark .social-link:hover {
  background-color: #9B4BDE;
}

.profile-username {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.profile-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dark-gray);
}

.dark .profile-name {
  color: #aaa;
}

.profile-stats {
  display: flex;
  gap: 30px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-count {
  font-weight: 700;
  font-size: 1.2rem;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.dark .stat-label {
  color: #aaa;
}

.profile-bio {
  font-size: 1rem;
  line-height: 1.5;
  max-width: 600px;
  margin-bottom: 10px;
}

.profile-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.dark .profile-meta {
  color: #aaa;
}

.profile-website {
  color: #6C13B3;
  text-decoration: none;
}

.profile-website:hover {
  text-decoration: underline;
}

.dark .profile-website {
  color: #9B4BDE;
}

.profile-actions {
  display: flex;
  gap: 10px;
  align-items: flex-start;
  padding-top: 20px;
}

.edit-profile-btn, .follow-btn, .message-btn, .share-profile-btn, .more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-profile-btn {
  border: 1px solid var(--light-gray);
  color: var(--text-color);
  text-decoration: none;
}

.dark .edit-profile-btn {
  border-color: #2a2a2a;
  color: var(--white);
}

.follow-btn {
  background-color: #6C13B3;
  color: var(--white);
  border: none;
}

.follow-btn:hover {
  background-color: #5a0e9c;
}

.follow-btn.following {
  background-color: transparent;
  border: 1px solid var(--light-gray);
  color: var(--text-color);
}

.dark .follow-btn.following {
  border-color: #2a2a2a;
  color: var(--white);
}

.message-btn, .share-profile-container {
  position: relative;
}

.share-profile-btn, .more-btn {
  border: 1px solid var(--light-gray);
  padding: 10px;
}

.share-options-dropdown {
  position: absolute;
  top: 45px;
  right: 0;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
  width: 200px;
  z-index: 10;
  overflow: hidden;
}

.dark .share-options-dropdown {
  background-color: #1e1e1e;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.4);
}

.share-option {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  padding: 12px 16px;
  text-align: left;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-color);
  transition: background-color 0.2s;
  text-decoration: none;
}

.dark .share-option {
  color: var(--white);
}

.share-option:hover {
  background-color: var(--gray);
}

.dark .share-option:hover {
  background-color: #2a2a2a;
}

.dark .message-btn,
.dark .share-profile-btn,
.dark .more-btn {
  border-color: #2a2a2a;
  color: var(--white);
}

.more-options {
  position: relative;
}

.options-dropdown {
  position: absolute;
  top: 45px;
  right: 0;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
  width: 180px;
  z-index: 10;
  overflow: hidden;
}

.dark .options-dropdown {
  background-color: #1e1e1e;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.4);
}

.option-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  text-align: left;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-color);
  transition: background-color 0.2s;
}

.dark .option-item {
  color: var(--white);
}

.option-item:hover {
  background-color: var(--gray);
}

.dark .option-item:hover {
  background-color: #2a2a2a;
}

.profile-tabs {
  display: flex;
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: 30px;
  padding: 0 40px;
}

.dark .profile-tabs {
  border-bottom-color: #2a2a2a;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 15px 25px;
  font-weight: 600;
  color: var(--dark-gray);
  border-bottom: 2px solid transparent;
  cursor: pointer;
  background: transparent;
  transition: all 0.2s;
}

.dark .tab-btn {
  color: #aaa;
}

.tab-btn:hover {
  color: var(--text-color);
}

.dark .tab-btn:hover {
  color: var(--white);
}

.tab-btn.active {
  color: #6C13B3;
  border-bottom: 2px solid #6C13B3;
}

.dark .tab-btn.active {
  color: #9B4BDE;
  border-bottom-color: #9B4BDE;
}

.profile-content {
  padding: 0 40px 40px;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.video-item {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s;
  cursor: pointer;
}

.video-item:hover {
  transform: translateY(-5px);
}

.thumbnail-container {
  position: relative;
  aspect-ratio: 3/4;
  overflow: hidden;
}

.thumbnail-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.video-item:hover .thumbnail-container img {
  transform: scale(1.05);
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 50%);
  opacity: 0;
  transition: opacity 0.3s;
  display: flex;
  align-items: flex-end;
  padding: 15px;
}

.video-item:hover .video-overlay {
  opacity: 1;
}

.video-stats {
  display: flex;
  gap: 15px;
}

.video-stat {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--white);
  font-size: 0.9rem;
}

.video-info {
  padding: 12px 0;
}

.video-description {
  font-size: 0.9rem;
  margin-bottom: 5px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-date {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .video-date {
  color: #aaa;
}

/* Analytics Section */
.analytics-container {
  background-color: var(--white);
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.dark .analytics-container {
  background-color: #1e1e1e;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.analytics-header h3 {
  font-size: 1.3rem;
  font-weight: 600;
}

.time-filter {
  padding: 8px 16px;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  background-color: var(--white);
  font-size: 0.9rem;
}

.dark .time-filter {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.analytics-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 40px;
}

.analytics-card {
  background-color: var(--gray);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
}

.dark .analytics-card {
  background-color: #2a2a2a;
}

.analytics-card h4 {
  font-size: 1rem;
  margin-bottom: 10px;
  color: var(--dark-gray);
}

.dark .analytics-card h4 {
  color: #aaa;
}

.analytics-value {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.analytics-change {
  font-size: 0.9rem;
  font-weight: 600;
}

.analytics-change.positive {
  color: #33CC33;
}

.analytics-change.negative {
  color: #FF3366;
}

.top-videos {
  margin-top: 30px;
}

.top-videos h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.top-videos-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.top-video-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background-color: var(--gray);
  border-radius: 12px;
}

/* Music Section */
.section-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 25px;
}

.music-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.music-item {
  background-color: var(--white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.dark .music-item {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.music-item:hover {
  transform: translateY(-5px);
}

.music-cover {
  position: relative;
  aspect-ratio: 1/1;
}

.music-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.play-overlay svg {
  color: white;
  background: rgba(108, 19, 179, 0.8);
  border-radius: 50%;
  padding: 10px;
}

.music-item:hover .play-overlay {
  opacity: 1;
}

.music-info {
  padding: 15px;
}

.music-title {
  font-weight: 600;
  margin-bottom: 5px;
}

.music-artist {
  color: var(--dark-gray);
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.dark .music-artist {
  color: #aaa;
}

.music-used {
  font-size: 0.8rem;
  color: #6C13B3;
  font-weight: 500;
}

.dark .music-used {
  color: #9B4BDE;
}

/* Drafts Section */
.draft-item {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.2s;
  cursor: pointer;
  background-color: var(--white);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.dark .draft-item {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.draft-item:hover {
  transform: translateY(-5px);
}

.draft-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 50%, rgba(0,0,0,0.1) 100%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 15px;
  opacity: 0;
  transition: opacity 0.3s;
}

.draft-item:hover .draft-overlay {
  opacity: 1;
}

.draft-duration {
  align-self: flex-end;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.draft-actions {
  display: flex;
  gap: 10px;
}

.draft-action {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.draft-action.edit {
  background-color: white;
  color: #333;
}

.draft-action.publish {
  background-color: #6C13B3;
  color: white;
}

.draft-action:hover {
  transform: translateY(-2px);
}

.draft-info {
  padding: 12px;
}

.draft-description {
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.draft-date {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .draft-date {
  color: #aaa;
}

/* Analytics Charts */
.analytics-charts {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 40px;
}

.analytics-chart {
  background-color: var(--white);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.dark .analytics-chart {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.analytics-chart h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
}

.dark .top-video-item {
  background-color: #2a2a2a;
}

.top-video-thumbnail {
  width: 120px;
  height: 160px;
  object-fit: cover;
  border-radius: 8px;
}

.top-video-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.top-video-description {
  font-size: 1rem;
  margin-bottom: 10px;
}

.top-video-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.dark .top-video-stats {
  color: #aaa;
}

/* For mobile devices */
@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    padding: 20px;
  }

  .profile-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 15px;
  }

  .profile-avatar-container {
    margin-top: -70px;
  }

  .profile-avatar {
    width: 120px;
    height: 120px;
  }

  .profile-details {
    align-items: center;
  }

  .profile-stats {
    justify-content: center;
  }

  .profile-bio {
    max-width: 100%;
  }

  .profile-meta {
    justify-content: center;
  }

  .profile-actions {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .profile-tabs {
    padding: 0 20px;
    overflow-x: auto;
    justify-content: flex-start;
  }

  .tab-btn {
    padding: 15px;
    white-space: nowrap;
  }

  .profile-content {
    padding: 0 20px 20px;
  }

  .video-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .analytics-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .top-video-item {
    flex-direction: column;
  }

  .top-video-thumbnail {
    width: 100%;
    height: 200px;
  }
}
