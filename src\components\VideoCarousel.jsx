import React, { useState, useRef, useEffect } from 'react';
import { FiChevronLeft, FiChevronRight, FiPlay } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import './VideoCarousel.css';

const VideoCarousel = ({ 
  title, 
  videos, 
  onVideoClick,
  showAuthor = true,
  showViews = true,
  showDuration = true,
  aspectRatio = '16:9',
  size = 'medium',
  seeAllLink = null
}) => {
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  
  const carouselRef = useRef(null);
  
  // Check if arrows should be shown
  const checkArrows = () => {
    if (!carouselRef.current) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = carouselRef.current;
    setShowLeftArrow(scrollLeft > 0);
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
  };
  
  useEffect(() => {
    const carousel = carouselRef.current;
    if (carousel) {
      carousel.addEventListener('scroll', checkArrows);
      // Initial check
      checkArrows();
      
      // Check again after content might have loaded
      setTimeout(checkArrows, 500);
    }
    
    return () => {
      if (carousel) {
        carousel.removeEventListener('scroll', checkArrows);
      }
    };
  }, [videos]);
  
  const scrollLeft = () => {
    if (!carouselRef.current) return;
    
    const itemWidth = carouselRef.current.querySelector('.carousel-item')?.offsetWidth || 300;
    carouselRef.current.scrollBy({ left: -itemWidth * 2, behavior: 'smooth' });
  };
  
  const scrollRight = () => {
    if (!carouselRef.current) return;
    
    const itemWidth = carouselRef.current.querySelector('.carousel-item')?.offsetWidth || 300;
    carouselRef.current.scrollBy({ left: itemWidth * 2, behavior: 'smooth' });
  };
  
  // Mouse drag functionality
  const handleMouseDown = (e) => {
    if (!carouselRef.current) return;
    
    setIsDragging(true);
    setStartX(e.pageX - carouselRef.current.offsetLeft);
    setScrollLeft(carouselRef.current.scrollLeft);
  };
  
  const handleMouseUp = () => {
    setIsDragging(false);
  };
  
  const handleMouseMove = (e) => {
    if (!isDragging || !carouselRef.current) return;
    
    e.preventDefault();
    const x = e.pageX - carouselRef.current.offsetLeft;
    const walk = (x - startX) * 2; // Scroll speed multiplier
    carouselRef.current.scrollLeft = scrollLeft - walk;
  };
  
  const handleMouseLeave = () => {
    setIsDragging(false);
  };
  
  // Format duration from seconds to MM:SS
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };
  
  // Format view count with K, M, B suffixes
  const formatViews = (views) => {
    if (views >= 1000000000) {
      return (views / 1000000000).toFixed(1) + 'B';
    }
    if (views >= 1000000) {
      return (views / 1000000).toFixed(1) + 'M';
    }
    if (views >= 1000) {
      return (views / 1000).toFixed(1) + 'K';
    }
    return views.toString();
  };
  
  // Get aspect ratio padding
  const getAspectRatioPadding = () => {
    switch (aspectRatio) {
      case '1:1':
        return '100%';
      case '4:3':
        return '75%';
      case '9:16':
        return '177.78%';
      default: // 16:9
        return '56.25%';
    }
  };
  
  // Get size class
  const getSizeClass = () => {
    switch (size) {
      case 'small':
        return 'carousel-small';
      case 'large':
        return 'carousel-large';
      default:
        return 'carousel-medium';
    }
  };
  
  return (
    <div className={`video-carousel ${getSizeClass()}`}>
      <div className="carousel-header">
        <h3 className="carousel-title">{title}</h3>
        {seeAllLink && (
          <Link to={seeAllLink} className="see-all-link">
            See All
          </Link>
        )}
      </div>
      
      <div className="carousel-container">
        {showLeftArrow && (
          <button className="carousel-arrow left-arrow" onClick={scrollLeft} aria-label="Scroll left">
            <FiChevronLeft />
          </button>
        )}
        
        <div 
          className="carousel-items"
          ref={carouselRef}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
        >
          {videos.map(video => (
            <div 
              key={video.id} 
              className="carousel-item"
              onClick={() => onVideoClick && onVideoClick(video)}
            >
              <div 
                className="carousel-thumbnail-container"
                style={{ paddingBottom: getAspectRatioPadding() }}
              >
                <img 
                  src={video.thumbnail || video.videoUrl} 
                  alt={video.title || video.caption} 
                  className="carousel-thumbnail"
                />
                
                <div className="carousel-overlay">
                  <div className="play-icon">
                    <FiPlay />
                  </div>
                </div>
                
                {showDuration && video.duration && (
                  <div className="video-duration">
                    {formatDuration(video.duration)}
                  </div>
                )}
              </div>
              
              <div className="carousel-item-info">
                <h4 className="video-title">
                  {video.title || video.caption?.substring(0, 60) || 'Untitled Video'}
                </h4>
                
                {showAuthor && (
                  <div className="video-author">
                    {video.userAvatar && (
                      <img 
                        src={video.userAvatar} 
                        alt={video.username || video.name} 
                        className="author-avatar"
                      />
                    )}
                    <span className="author-name">
                      {video.name || video.username || 'Unknown'}
                      {video.isVerified && (
                        <span className="verified-badge">✓</span>
                      )}
                    </span>
                  </div>
                )}
                
                {showViews && video.views && (
                  <div className="video-stats">
                    <span className="video-views">{formatViews(video.views)} views</span>
                    {video.timestamp && (
                      <>
                        <span className="dot-separator">•</span>
                        <span className="video-time">{formatTimeAgo(video.timestamp)}</span>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {showRightArrow && (
          <button className="carousel-arrow right-arrow" onClick={scrollRight} aria-label="Scroll right">
            <FiChevronRight />
          </button>
        )}
      </div>
    </div>
  );
};

// Helper function to format time ago
const formatTimeAgo = (timestamp) => {
  const now = Date.now();
  const diff = now - timestamp;
  
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  const weeks = Math.floor(days / 7);
  const months = Math.floor(days / 30);
  const years = Math.floor(days / 365);
  
  if (years > 0) return `${years} ${years === 1 ? 'year' : 'years'} ago`;
  if (months > 0) return `${months} ${months === 1 ? 'month' : 'months'} ago`;
  if (weeks > 0) return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
  if (days > 0) return `${days} ${days === 1 ? 'day' : 'days'} ago`;
  if (hours > 0) return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  if (minutes > 0) return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
  return 'Just now';
};

export default VideoCarousel;
