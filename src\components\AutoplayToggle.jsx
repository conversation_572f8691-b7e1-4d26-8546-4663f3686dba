import React from 'react';
import { FiPlay, FiPause } from 'react-icons/fi';
import './AutoplayToggle.css';

const AutoplayToggle = ({ isEnabled, onChange, size = 'medium' }) => {
  // Size variants: 'small', 'medium', 'large'
  
  return (
    <div className={`autoplay-toggle size-${size}`}>
      <span className="autoplay-label">Autoplay</span>
      <button 
        className={`toggle-button ${isEnabled ? 'enabled' : 'disabled'}`}
        onClick={() => onChange(!isEnabled)}
        aria-label={isEnabled ? 'Disable autoplay' : 'Enable autoplay'}
        title={isEnabled ? 'Disable autoplay' : 'Enable autoplay'}
      >
        <div className="toggle-track">
          <div className="toggle-indicator">
            {isEnabled ? <FiPlay className="toggle-icon" /> : <FiPause className="toggle-icon" />}
          </div>
        </div>
        <span className="toggle-status">{isEnabled ? 'ON' : 'OFF'}</span>
      </button>
    </div>
  );
};

export default AutoplayToggle;
