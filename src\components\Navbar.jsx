import React, { useState, useContext, useEffect, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FiSearch, FiPlus, FiUser, FiLogIn, FiBell, FiSettings, FiMoon, FiSun, FiMessageCircle, FiMenu, FiX, FiHome, FiUsers, FiCompass, FiRadio } from 'react-icons/fi';
import { ThemeContext } from '../contexts/ThemeContext';
import './Navbar.css';

function Navbar() {
  const [searchValue, setSearchValue] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showMobileSearch, setShowMobileSearch] = useState(false);
  const { isDarkMode, toggleTheme } = useContext(ThemeContext);
  const location = useLocation();
  const dropdownRef = useRef(null);
  const mobileMenuRef = useRef(null);

  const handleSearch = (e) => {
    e.preventDefault();
    console.log('Searching for:', searchValue);
    setShowMobileSearch(false);
    // In a real app, you would navigate to search results
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {
        setShowMobileMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setShowMobileMenu(false);
    setShowMobileSearch(false);
  }, [location.pathname]);

  return (
    <nav className={`navbar ${isDarkMode ? 'dark' : ''}`}>
      <div className="navbar-left">
        {/* Mobile Menu Button */}
        <button
          className="mobile-menu-btn"
          onClick={() => setShowMobileMenu(!showMobileMenu)}
          aria-label={showMobileMenu ? "Close menu" : "Open menu"}
        >
          {showMobileMenu ? <FiX size={20} /> : <FiMenu size={20} />}
        </button>

        <Link to="/" className="logo" style={{ background: isDarkMode ? 'rgba(155, 75, 222, 0.1)' : 'rgba(108, 19, 179, 0.05)', padding: '10px 15px', borderRadius: '12px' }}>
          <div className="vibevid-logo-small">
            <div className="wave-1-small"></div>
            <div className="wave-2-small"></div>
            <div className="play-button-small"></div>
          </div>
          <span className="logo-text" style={{ textShadow: isDarkMode ? '0 0 15px rgba(155, 75, 222, 0.5)' : '0 0 15px rgba(108, 19, 179, 0.3)' }}>VibeVid</span>
        </Link>
      </div>

      <div className="navbar-center">
        <form className="search-form" onSubmit={handleSearch}>
          <input
            type="text"
            placeholder="Search accounts and videos"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
          <button type="submit" className="search-btn">
            <FiSearch />
          </button>
        </form>
      </div>

      <div className="navbar-right">
        {/* Mobile Search Button */}
        <button
          className="mobile-search-btn"
          onClick={() => setShowMobileSearch(!showMobileSearch)}
        >
          <FiSearch />
        </button>

        <button
          className="theme-toggle"
          onClick={toggleTheme}
          style={{
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            backgroundColor: isDarkMode ? '#2a2a2a' : 'var(--gray)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: 'none',
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)',
            color: isDarkMode ? '#9B4BDE' : '#6C13B3',
            transition: 'all 0.3s ease'
          }}
        >
          {isDarkMode ? <FiSun size={20} /> : <FiMoon size={20} />}
        </button>

        <Link to="/messages" className="icon-btn desktop-only">
          <FiMessageCircle />
        </Link>

        <Link to="/notifications" className="icon-btn desktop-only">
          <FiBell />
          <span className="notification-badge">3</span>
        </Link>

        <Link to="/upload" className="upload-btn">
          <FiPlus />
          <span className="upload-text">Upload</span>
        </Link>

        <div className="user-menu" ref={dropdownRef}>
          <button
            className="avatar-btn"
            onClick={() => setShowDropdown(!showDropdown)}
          >
            <img src="https://placehold.co/40" alt="User avatar" />
          </button>

          {showDropdown && (
            <div className="dropdown-menu">
              <Link to="/profile/user123" className="dropdown-item">
                <FiUser />
                <span>Profile</span>
              </Link>
              <Link to="/settings" className="dropdown-item">
                <FiSettings />
                <span>Settings</span>
              </Link>
              <button className="dropdown-item logout">
                <FiLogIn />
                <span>Log Out</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Search Overlay */}
      {showMobileSearch && (
        <div className="mobile-search-overlay">
          <form className="mobile-search-form" onSubmit={handleSearch}>
            <input
              type="text"
              placeholder="Search accounts and videos"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              autoFocus
            />
            <button type="submit" className="mobile-search-submit">
              <FiSearch />
            </button>
            <button
              type="button"
              className="mobile-search-close"
              onClick={() => setShowMobileSearch(false)}
            >
              <FiX />
            </button>
          </form>
        </div>
      )}

      {/* Mobile Menu */}
      {showMobileMenu && (
        <div className="mobile-menu-overlay" ref={mobileMenuRef}>
          <div className="mobile-menu">
            <div className="mobile-menu-header">
              <h3>Menu</h3>
              <button
                className="mobile-menu-close"
                onClick={() => setShowMobileMenu(false)}
              >
                <FiX />
              </button>
            </div>

            <div className="mobile-menu-content">
              <div className="mobile-nav-section">
                <h4>Navigation</h4>
                <Link to="/" className={`mobile-nav-item ${location.pathname === '/' ? 'active' : ''}`}>
                  <FiHome />
                  <span>For You</span>
                </Link>
                <Link to="/following" className={`mobile-nav-item ${location.pathname === '/following' ? 'active' : ''}`}>
                  <FiUsers />
                  <span>Following</span>
                </Link>
                <Link to="/explore" className={`mobile-nav-item ${location.pathname === '/explore' ? 'active' : ''}`}>
                  <FiCompass />
                  <span>Explore</span>
                </Link>
                <Link to="/live" className={`mobile-nav-item ${location.pathname === '/live' ? 'active' : ''}`}>
                  <FiRadio />
                  <span>LIVE</span>
                </Link>
              </div>

              <div className="mobile-nav-section">
                <h4>Account</h4>
                <Link to="/profile/user123" className="mobile-nav-item">
                  <FiUser />
                  <span>Profile</span>
                </Link>
                <Link to="/messages" className="mobile-nav-item">
                  <FiMessageCircle />
                  <span>Messages</span>
                </Link>
                <Link to="/notifications" className="mobile-nav-item">
                  <FiBell />
                  <span>Notifications</span>
                  <span className="mobile-notification-badge">3</span>
                </Link>
                <Link to="/settings" className="mobile-nav-item">
                  <FiSettings />
                  <span>Settings</span>
                </Link>
              </div>

              <div className="mobile-nav-section">
                <button className="mobile-nav-item logout">
                  <FiLogIn />
                  <span>Log Out</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}

export default Navbar;
