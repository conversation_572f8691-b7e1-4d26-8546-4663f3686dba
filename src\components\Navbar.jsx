import React, { useState, useContext } from 'react';
import { Link } from 'react-router-dom';
import { FiSearch, FiPlus, FiUser, FiLogIn, FiBell, FiSettings, FiMoon, FiSun, FiMessageCircle } from 'react-icons/fi';
import { ThemeContext } from '../context/ThemeContext';
import './Navbar.css';

function Navbar() {
  const [searchValue, setSearchValue] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const { darkMode, toggleTheme } = useContext(ThemeContext);

  const handleSearch = (e) => {
    e.preventDefault();
    console.log('Searching for:', searchValue);
    // In a real app, you would navigate to search results
  };

  return (
    <nav className={`navbar ${darkMode ? 'dark' : ''}`}>
      <div className="navbar-left">
        <Link to="/" className="logo" style={{ background: darkMode ? 'rgba(155, 75, 222, 0.1)' : 'rgba(108, 19, 179, 0.05)', padding: '10px 15px', borderRadius: '12px' }}>
          <div className="vibevid-logo-small">
            <div className="wave-1-small"></div>
            <div className="wave-2-small"></div>
            <div className="play-button-small"></div>
          </div>
          <span style={{ textShadow: darkMode ? '0 0 15px rgba(155, 75, 222, 0.5)' : '0 0 15px rgba(108, 19, 179, 0.3)' }}>VibeVid</span>
        </Link>
      </div>

      <div className="navbar-center">
        <form className="search-form" onSubmit={handleSearch}>
          <input
            type="text"
            placeholder="Search accounts and videos"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
          />
          <button type="submit" className="search-btn">
            <FiSearch />
          </button>
        </form>
      </div>

      <div className="navbar-right">
        <button
          className="theme-toggle"
          onClick={toggleTheme}
          style={{
            width: '40px',
            height: '40px',
            borderRadius: '50%',
            backgroundColor: darkMode ? '#2a2a2a' : 'var(--gray)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: 'none',
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)',
            color: darkMode ? '#9B4BDE' : '#6C13B3',
            transition: 'all 0.3s ease'
          }}
        >
          {darkMode ? <FiSun size={20} /> : <FiMoon size={20} />}
        </button>

        <Link to="/messages" className="icon-btn">
          <FiMessageCircle />
        </Link>

        <Link to="/notifications" className="icon-btn">
          <FiBell />
          <span className="notification-badge">3</span>
        </Link>

        <Link to="/upload" className="upload-btn">
          <FiPlus />
          <span>Upload</span>
        </Link>

        <div className="user-menu">
          <button
            className="avatar-btn"
            onClick={() => setShowDropdown(!showDropdown)}
          >
            <img src="https://placehold.co/40" alt="User avatar" />
          </button>

          {showDropdown && (
            <div className="dropdown-menu">
              <Link to="/profile/user123" className="dropdown-item">
                <FiUser />
                <span>Profile</span>
              </Link>
              <Link to="/settings" className="dropdown-item">
                <FiSettings />
                <span>Settings</span>
              </Link>
              <button className="dropdown-item logout">
                <FiLogIn />
                <span>Log Out</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
}

export default Navbar;
