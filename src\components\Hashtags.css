.hashtags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 5px 0;
}

.hashtag-link {
  display: inline-block;
  transition: all 0.2s ease;
  position: relative;
  font-weight: 600;
  text-decoration: none;
}

/* Size variants */
.size-small .hashtag-link {
  font-size: 0.8rem;
}

.size-medium .hashtag-link {
  font-size: 0.95rem;
}

.size-large .hashtag-link {
  font-size: 1.1rem;
}

/* Color variants */
.color-default {
  color: #6C13B3;
}

.dark .color-default {
  color: #9B4BDE;
}

.color-primary {
  color: #FF3366;
}

.dark .color-primary {
  color: #FF5C85;
}

.color-gradient .hashtag-link {
  background: linear-gradient(90deg, #6C13B3, #FF3366);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Hover effects */
.hashtag-link:hover {
  transform: translateY(-2px);
}

.hashtag-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.color-gradient .hashtag-link::after {
  background: linear-gradient(90deg, #6C13B3, #FF3366);
}

.hashtag-link:hover::after {
  width: 100%;
}

/* Animation for new hashtags */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hashtag-link {
  animation: fadeIn 0.3s ease forwards;
}
