import React, { useState } from 'react';
import { FiThumbsUp, FiThumbsDown, FiRefreshCw, FiStar, FiTrendingUp, FiClock, FiHeart } from 'react-icons/fi';
import './PersonalizedRecommendations.css';

const PersonalizedRecommendations = ({ onCategorySelect }) => {
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedbackType, setFeedbackType] = useState(null);

  // Recommendation categories
  const categories = [
    { id: 'trending', name: 'Trending Now', icon: <FiTrendingUp />, color: '#FF3366' },
    { id: 'foryou', name: 'Personalized', icon: <FiStar />, color: '#6C13B3' },
    { id: 'new', name: 'New Content', icon: <FiClock />, color: '#00CCFF' },
    { id: 'favorites', name: 'From Favorites', icon: <FiHeart />, color: '#FF9900' }
  ];

  // Interest topics
  const interestTopics = [
    { id: 'dance', name: 'Dance', selected: false },
    { id: 'comedy', name: 'Comedy', selected: false },
    { id: 'cooking', name: 'Cooking', selected: false },
    { id: 'fitness', name: 'Fitness', selected: false },
    { id: 'travel', name: 'Travel', selected: false },
    { id: 'music', name: 'Music', selected: false },
    { id: 'pets', name: 'Pets', selected: false },
    { id: 'gaming', name: 'Gaming', selected: false },
    { id: 'art', name: 'Art', selected: false },
    { id: 'beauty', name: 'Beauty', selected: false }
  ];

  const [topics, setTopics] = useState(interestTopics);

  const handleCategoryClick = (categoryId) => {
    if (onCategorySelect) {
      onCategorySelect(categoryId);
    }
  };

  const handleTopicToggle = (topicId) => {
    setTopics(topics.map(topic => 
      topic.id === topicId ? { ...topic, selected: !topic.selected } : topic
    ));
    
    // Update selected categories
    const updatedSelectedCategories = topics
      .filter(topic => topic.id === topicId ? !topic.selected : topic.selected)
      .map(topic => topic.id);
    
    setSelectedCategories(updatedSelectedCategories);
  };

  const handleFeedback = (type) => {
    setFeedbackType(type);
    setShowFeedback(true);
    
    // Hide feedback message after 3 seconds
    setTimeout(() => {
      setShowFeedback(false);
      setFeedbackType(null);
    }, 3000);
  };

  const handleRefresh = () => {
    // Simulate refreshing recommendations
    const refreshAnimation = document.querySelector('.refresh-icon');
    refreshAnimation.classList.add('refreshing');
    
    setTimeout(() => {
      refreshAnimation.classList.remove('refreshing');
    }, 1000);
    
    // In a real app, this would trigger a refresh of recommendations
  };

  return (
    <div className="personalized-recommendations">
      <div className="recommendations-header">
        <h3>Personalized For You</h3>
        <div className="recommendations-actions">
          <button 
            className="feedback-btn like-btn" 
            onClick={() => handleFeedback('like')}
            title="I like these recommendations"
          >
            <FiThumbsUp />
          </button>
          <button 
            className="feedback-btn dislike-btn" 
            onClick={() => handleFeedback('dislike')}
            title="Show me different content"
          >
            <FiThumbsDown />
          </button>
          <button 
            className="refresh-btn" 
            onClick={handleRefresh}
            title="Refresh recommendations"
          >
            <FiRefreshCw className="refresh-icon" />
          </button>
        </div>
      </div>
      
      {showFeedback && (
        <div className={`feedback-message ${feedbackType}`}>
          {feedbackType === 'like' ? (
            <p>Thanks! We'll show more content like this.</p>
          ) : (
            <p>We'll adjust your recommendations.</p>
          )}
        </div>
      )}
      
      <div className="recommendation-categories">
        {categories.map(category => (
          <button
            key={category.id}
            className="category-card"
            style={{ backgroundColor: `${category.color}10`, borderColor: category.color }}
            onClick={() => handleCategoryClick(category.id)}
          >
            <div className="category-icon" style={{ color: category.color }}>
              {category.icon}
            </div>
            <span className="category-name">{category.name}</span>
          </button>
        ))}
      </div>
      
      <div className="interest-topics">
        <h4>Customize Your Feed</h4>
        <p>Select topics you're interested in:</p>
        <div className="topics-grid">
          {topics.map(topic => (
            <button
              key={topic.id}
              className={`topic-btn ${topic.selected ? 'selected' : ''}`}
              onClick={() => handleTopicToggle(topic.id)}
            >
              {topic.name}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PersonalizedRecommendations;
