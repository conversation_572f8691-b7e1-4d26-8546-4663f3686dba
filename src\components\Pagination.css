.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin: 30px 0;
}

.pagination-arrow {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-color);
  font-size: 1.2rem;
}

.dark .pagination-arrow {
  background-color: #1e1e1e;
  border-color: #2a2a2a;
  color: var(--white);
}

.pagination-arrow:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-arrow:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-numbers {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-color);
  font-weight: 500;
}

.dark .pagination-number {
  background-color: #1e1e1e;
  border-color: #2a2a2a;
  color: var(--white);
}

.pagination-number:hover:not(.active) {
  background-color: var(--gray);
}

.dark .pagination-number:hover:not(.active) {
  background-color: #2a2a2a;
}

.pagination-number.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.dark .pagination-number.active {
  background-color: #9B4BDE;
  border-color: #9B4BDE;
}

.pagination-ellipsis {
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark-gray);
  font-weight: 600;
}

.dark .pagination-ellipsis {
  color: #aaa;
}

.pagination-info {
  font-size: 0.9rem;
  color: var(--dark-gray);
  margin-left: 10px;
}

.dark .pagination-info {
  color: #aaa;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .pagination {
    flex-wrap: wrap;
  }
  
  .pagination-numbers {
    order: 3;
    width: 100%;
    justify-content: center;
    margin-top: 10px;
  }
  
  .pagination-info {
    order: 2;
  }
}

@media (max-width: 480px) {
  .pagination-number {
    width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }
  
  .pagination-arrow {
    width: 35px;
    height: 35px;
  }
  
  .pagination-info {
    font-size: 0.8rem;
  }
}
