.video-card {
  display: flex;
  flex-direction: column;
  padding: 20px 0 80px;
  border-bottom: 1px solid var(--light-gray);
  max-width: 800px;
  margin: 0 auto 80px;
  transition: transform 0.2s;
  user-select: none;
  position: relative;
  z-index: 1;
}

.dark .video-card {
  border-bottom-color: #2a2a2a;
}

.video-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--white);
  border-radius: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--light-gray);
  position: relative;
  z-index: 5;
}

.dark .video-card-header {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid #2a2a2a;
}

.video-user {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: inherit;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.video-user:hover .user-avatar {
  border-color: #6C13B3;
}

.dark .video-user:hover .user-avatar {
  border-color: #9B4BDE;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-name-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.username {
  font-weight: 700;
  font-size: 1.1rem;
  color: var(--text-color);
  letter-spacing: 0.2px;
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.8rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.handle {
  font-size: 0.9rem;
  color: var(--dark-gray);
  font-weight: 500;
}

.dark .handle {
  color: #aaa;
}

.video-header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.name {
  font-size: 0.9rem;
  color: var(--dark-gray);
}

/* Follow button styles are now handled by global button classes */

.options-menu {
  position: relative;
}

.options-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dark .options-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.options-btn:hover {
  background-color: var(--light-gray);
}

.dark .options-btn:hover {
  background-color: #3a3a3a;
}

.options-dropdown {
  position: absolute;
  top: 45px;
  right: 0;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
  width: 180px;
  z-index: 10;
  overflow: hidden;
}

.dark .options-dropdown {
  background-color: #1e1e1e;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.4);
}

.option-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  text-align: left;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-color);
  transition: background-color 0.2s;
}

.dark .option-item {
  color: var(--white);
}

.option-item:hover {
  background-color: var(--gray);
}

.dark .option-item:hover {
  background-color: #2a2a2a;
}

.video-caption {
  margin-bottom: 15px;
}

.video-caption p {
  margin-bottom: 8px;
  font-size: 0.95rem;
  line-height: 1.4;
}

.video-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.tag {
  color: #6C13B3;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: color 0.2s;
}

.dark .tag {
  color: #9B4BDE;
}

.tag:hover {
  color: #5a0e9c;
}

.dark .tag:hover {
  color: #8a3dcf;
}

.video-music {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  color: var(--dark-gray);
  margin-top: 10px;
}

.dark .video-music {
  color: #aaa;
}

.video-container {
  position: relative;
  border-radius: 8px;
  overflow: visible;
  margin-bottom: 100px;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.video-wrapper {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  width: 100%;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.video-player-container {
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.video-player {
  width: 100%;
  max-height: 90vh;
  min-height: 650px;
  object-fit: contain;
  background-color: black;
  display: block;
  border-radius: 12px;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.video-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(108, 19, 179, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  transition: transform 0.2s, background-color 0.2s;
}

.play-button:hover {
  transform: scale(1.1);
  background-color: rgba(108, 19, 179, 1);
}

.dark .play-button {
  background-color: rgba(155, 75, 222, 0.8);
}

.dark .play-button:hover {
  background-color: rgba(155, 75, 222, 1);
}

/* Video Controls */
.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 5;
}

.video-wrapper:hover .video-controls {
  opacity: 1;
}

.progress-container {
  margin-bottom: 10px;
  cursor: pointer;
  position: relative;
}

.progress-bar {
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 5px;
  transition: height 0.2s ease;
  position: relative;
}

.progress-container:hover .progress-bar {
  height: 6px;
}

.progress-fill {
  height: 100%;
  background-color: #6C13B3;
  border-radius: 2px;
  position: relative;
}

.progress-hover-indicator {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background-color: #6C13B3;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  z-index: 2;
}

.progress-hover-tooltip {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: nowrap;
}

.dark .progress-fill {
  background-color: #9B4BDE;
}

.time-display {
  display: flex;
  justify-content: space-between;
  color: white;
  font-size: 0.8rem;
}

.control-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
}

.control-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background-color: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.volume-control {
  position: relative;
}

.volume-slider-container {
  position: absolute;
  bottom: 45px;
  left: 50%;
  transform: translateX(-50%) rotate(-90deg);
  width: 100px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  transform-origin: bottom center;
  z-index: 10;
}

.volume-slider {
  width: 100%;
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #6C13B3;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #6C13B3;
  cursor: pointer;
  border: none;
}

.dark .volume-slider::-webkit-slider-thumb {
  background-color: #9B4BDE;
}

.dark .volume-slider::-moz-range-thumb {
  background-color: #9B4BDE;
}

.video-stats {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: auto;
  margin-right: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: white;
  font-size: 0.8rem;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 15px;
}

.video-actions {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  padding: 15px 20px;
  margin-top: -20px;
  margin-bottom: 20px;
  background-color: var(--white);
  border-radius: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  z-index: 100;
  border: 1px solid #e0e0e0;
}

.dark .video-actions {
  background-color: #1e1e1e;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  border: 1px solid #2a2a2a;
}

.video-actions::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, #6C13B3, #FF3366);
  border-radius: 20px 20px 0 0;
}

.dark .video-actions::before {
  background: linear-gradient(to right, #9B4BDE, #FF5C85);
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-color);
  transition: all 0.3s ease;
  position: relative;
  padding: 12px;
  border-radius: 12px;
  height: 100%;
  min-width: 60px;
  flex: 1;
}

.dark .action-btn {
  color: var(--white);
}

.dark .action-btn span {
  color: var(--white);
}

.action-btn:hover {
  transform: translateY(-2px);
  background-color: var(--gray);
}

.dark .action-btn:hover {
  background-color: #2a2a2a;
}

.action-btn::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 3px;
  background-color: #6C13B3;
  transform: translateX(-50%);
  transition: width 0.3s ease;
  border-radius: 3px;
}

.dark .action-btn::after {
  background-color: #9B4BDE;
}

.action-btn:hover::after {
  width: 70%;
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  border: none;
}

.share-icon {
  transition: all 0.3s ease;
}

.action-btn:hover .share-icon {
  transform: rotate(15deg);
}

.dark .action-icon {
  background-color: #2a2a2a;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.action-btn:hover .action-icon {
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(108, 19, 179, 0.15);
  background-color: #6C13B3;
  color: white;
}

.dark .action-btn:hover .action-icon {
  box-shadow: 0 6px 15px rgba(155, 75, 222, 0.2);
  background-color: #9B4BDE;
  color: white;
}

.action-btn span {
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  min-width: 24px;
  text-align: center;
  letter-spacing: 0.2px;
  color: var(--text-color);
}

.action-btn:hover span {
  transform: scale(1.1);
}

.action-btn.liked {
  color: #FF3366;
}

.action-btn.liked .action-icon {
  color: white;
  background-color: #FF3366;
  box-shadow: 0 4px 12px rgba(255, 51, 102, 0.3);
}

.action-btn.liked::after {
  background-color: #FF3366;
  width: 70%;
}

.action-btn.saved {
  color: #6C13B3;
}

.dark .action-btn.saved {
  color: #9B4BDE;
}

.action-btn.saved .action-icon {
  color: white;
  background-color: #6C13B3;
  box-shadow: 0 4px 12px rgba(108, 19, 179, 0.3);
}

.dark .action-btn.saved .action-icon {
  color: white;
  background-color: #9B4BDE;
  box-shadow: 0 4px 12px rgba(155, 75, 222, 0.3);
}

.action-btn.saved::after {
  background-color: #6C13B3;
  width: 70%;
}

.dark .action-btn.saved::after {
  background-color: #9B4BDE;
}

/* For mobile devices */
@media (max-width: 768px) {
  .video-card {
    padding: 15px 0;
  }

  .video-player {
    max-height: 70vh;
  }

  /* Video actions padding is handled below */

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
  }

  .video-actions {
    padding: 12px 15px;
    gap: 5px;
    border-radius: 15px;
    margin-top: -15px;
    margin-bottom: 15px;
  }

  .action-btn {
    padding: 8px 5px;
  }

  .action-btn span {
    font-size: 0.85rem;
  }

  .social-buttons-container {
    padding: 12px 15px;
    max-width: 95%;
  }
}

/* Video and Social Container */
.video-and-social-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 100%;
  margin-bottom: 40px;
}

/* Social Buttons Container */
.social-buttons-container {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  margin: -50px auto 20px;
  background-color: var(--white);
  border-radius: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  border: none;
  position: relative;
  z-index: 999;
  max-width: 85%;
  width: 100%;
}

.social-buttons-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(to right, #6C13B3, #FF3366);
  border-radius: 22px;
  z-index: -1;
}

.dark .social-buttons-container {
  background-color: #1e1e1e;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.dark .social-buttons-container::before {
  background: linear-gradient(to right, #9B4BDE, #FF5C85);
}

.social-button-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.social-btn {
  width: 52px;
  height: 52px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-color);
}

.dark .social-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.social-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.social-btn.liked {
  background-color: #FF3366;
  color: white;
}

.social-btn.disliked {
  background-color: #666;
  color: white;
}

.social-btn.saved {
  background-color: #6C13B3;
  color: white;
}

.dark .social-btn.saved {
  background-color: #9B4BDE;
}

.social-count {
  font-weight: 600;
  color: var(--dark-gray);
  font-size: 0.9rem;
}

.dark .social-count {
  color: #aaa;
}

.social-count.liked {
  color: #FF3366;
}

.social-count.saved {
  color: #6C13B3;
}

.dark .social-count.saved {
  color: #9B4BDE;
}

.share-options-dropdown {
  position: absolute;
  bottom: 60px;
  right: -20px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  padding: 15px;
  width: 280px;
  z-index: 10;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.dark .share-options-dropdown {
  background-color: #1e1e1e;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4);
}

.share-options-dropdown h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.dark .share-options-dropdown h4 {
  color: var(--white);
}

.share-options-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 10px;
  border-radius: 8px;
  background-color: var(--gray);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .share-option {
  background-color: #2a2a2a;
}

.share-option:hover {
  transform: translateY(-3px);
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .share-option:hover {
  background-color: rgba(155, 75, 222, 0.2);
}

.share-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
}

.share-icon.twitter {
  background-color: #1DA1F2;
}

.share-icon.facebook {
  background-color: #4267B2;
}

.share-icon.whatsapp {
  background-color: #25D366;
}

.share-icon.copy {
  background-color: #6C13B3;
}

.dark .share-icon.copy {
  background-color: #9B4BDE;
}

.share-option span {
  font-size: 0.8rem;
  color: var(--text-color);
}

.dark .share-option span {
  color: var(--white);
}

.tooltip-message {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 0.9rem;
  z-index: 1000;
  animation: fadeInOut 2s ease;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translate(-50%, 10px); }
  20% { opacity: 1; transform: translate(-50%, 0); }
  80% { opacity: 1; transform: translate(-50%, 0); }
  100% { opacity: 0; transform: translate(-50%, -10px); }
}
