import React, { useState, useEffect, useRef } from 'react';
import { FiUsers, FiEye, FiHeart, FiMessageSquare, FiGift, FiShare2, FiVolume2, FiVolumeX, FiMaximize, FiX, FiBookmark, FiMoreHorizontal, FiDownload } from 'react-icons/fi';
import './LiveStreamView.css';

const LiveStreamView = ({ stream, onClose }) => {
  const [isMuted, setIsMuted] = useState(true);
  const [streamDuration, setStreamDuration] = useState('00:00:00');
  const [streamLikes, setStreamLikes] = useState(stream?.likes || 876);
  const [isStreamLiked, setIsStreamLiked] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [chatMessages, setChatMessages] = useState([
    { id: 1, user: '<PERSON>', avatar: 'https://randomuser.me/api/portraits/women/44.jpg', message: 'Amazing performance! 🔥', time: '2:45 PM' },
    { id: 2, user: '<PERSON>', avatar: 'https://randomuser.me/api/portraits/men/22.jpg', message: 'Can you play some rock music next?', time: '2:46 PM' },
    { id: 3, user: 'Sophia Garcia', avatar: 'https://randomuser.me/api/portraits/women/56.jpg', message: 'Sent a Super Gift! 🎁', time: '2:47 PM', isGift: true },
    { id: 4, user: 'Daniel Lee', avatar: 'https://randomuser.me/api/portraits/men/45.jpg', message: 'How long have you been playing guitar?', time: '2:48 PM' }
  ]);
  const [newChatMessage, setNewChatMessage] = useState('');

  const videoContainerRef = useRef(null);
  const chatContainerRef = useRef(null);
  const durationIntervalRef = useRef(null);

  // Start duration timer when component mounts
  useEffect(() => {
    startDurationTimer();

    return () => {
      if (durationIntervalRef.current) {
        clearInterval(durationIntervalRef.current);
      }
    };
  }, []);

  // Scroll chat to bottom when new messages arrive
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatMessages]);

  // Simulate new chat messages
  useEffect(() => {
    const messageInterval = setInterval(() => {
      if (Math.random() > 0.7) {
        const users = [
          { name: 'Emma Wilson', avatar: 'https://randomuser.me/api/portraits/women/44.jpg' },
          { name: 'Michael Brown', avatar: 'https://randomuser.me/api/portraits/men/22.jpg' },
          { name: 'Sophia Garcia', avatar: 'https://randomuser.me/api/portraits/women/56.jpg' },
          { name: 'Daniel Lee', avatar: 'https://randomuser.me/api/portraits/men/45.jpg' }
        ];

        const messages = [
          'Great stream!',
          'Hello from California!',
          'Love your content!',
          'First time watching, this is awesome!',
          'Can you do a tutorial on this?',
          'Followed you! Keep up the good work!',
          'What camera are you using?',
          'The lighting looks great!',
          'How long have you been doing this?',
          '👍👍👍'
        ];

        const randomUser = users[Math.floor(Math.random() * users.length)];
        const randomMessage = messages[Math.floor(Math.random() * messages.length)];
        const isGift = Math.random() > 0.9;

        const newMessage = {
          id: Date.now(),
          user: randomUser.name,
          avatar: randomUser.avatar,
          message: isGift ? 'Sent a Super Gift! 🎁' : randomMessage,
          time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          isGift
        };

        setChatMessages(prev => [...prev, newMessage]);
      }
    }, 5000);

    return () => clearInterval(messageInterval);
  }, []);

  // Start a timer to update the stream duration
  const startDurationTimer = () => {
    durationIntervalRef.current = setInterval(() => {
      setStreamDuration(prev => {
        const [hours, minutes, seconds] = prev.split(':').map(Number);
        let newSeconds = seconds + 1;
        let newMinutes = minutes;
        let newHours = hours;

        if (newSeconds >= 60) {
          newSeconds = 0;
          newMinutes += 1;
        }

        if (newMinutes >= 60) {
          newMinutes = 0;
          newHours += 1;
        }

        return `${newHours.toString().padStart(2, '0')}:${newMinutes.toString().padStart(2, '0')}:${newSeconds.toString().padStart(2, '0')}`;
      });
    }, 1000);
  };

  // Toggle mute
  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  // Toggle fullscreen
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (videoContainerRef.current.requestFullscreen) {
        videoContainerRef.current.requestFullscreen();
      } else if (videoContainerRef.current.webkitRequestFullscreen) {
        videoContainerRef.current.webkitRequestFullscreen();
      } else if (videoContainerRef.current.msRequestFullscreen) {
        videoContainerRef.current.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }

    setIsFullscreen(!isFullscreen);
  };

  // Handle like button click
  const handleLikeClick = () => {
    if (isStreamLiked) {
      setStreamLikes(prev => prev - 1);
    } else {
      setStreamLikes(prev => prev + 1);
    }
    setIsStreamLiked(!isStreamLiked);
  };

  // Handle follow button click
  const handleFollowClick = () => {
    setIsFollowing(!isFollowing);
  };

  // Handle save button click
  const handleSaveClick = () => {
    setIsSaved(!isSaved);
  };

  // Handle sending a chat message
  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newChatMessage.trim()) {
      const newMessage = {
        id: Date.now(),
        user: 'You',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        message: newChatMessage,
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      setChatMessages(prev => [...prev, newMessage]);
      setNewChatMessage('');
    }
  };

  // Share the stream
  const shareStream = () => {
    if (navigator.share) {
      navigator.share({
        title: stream ? stream.title : 'Live Stream',
        text: `Check out this live stream on VibeVid!`,
        url: window.location.href
      })
      .catch(err => console.log('Error sharing:', err));
    } else {
      // Fallback for browsers that don't support Web Share API
      const shareUrl = window.location.href;
      navigator.clipboard.writeText(shareUrl)
        .then(() => alert('Stream link copied to clipboard!'))
        .catch(err => console.error('Could not copy text: ', err));
    }
  };

  return (
    <div className="live-stream-view">
      <div className="stream-header">
        <div className="stream-title-info">
          <div className="live-badge large">LIVE</div>
          <h2>{stream ? stream.title : 'Live Music Session 🎵'}</h2>
        </div>
        <button className="close-stream-btn" onClick={onClose}>
          <FiX />
        </button>
      </div>

      <div className="stream-content">
        <div className="stream-player">
          <div className="player-container">
            <div className="video-container" ref={videoContainerRef}>
              <img
                src={stream ? stream.thumbnail : "https://placehold.co/1200x675/6C13B3/FFFFFF?text=Live+Stream+Player"}
                alt="Live Stream"
                className="stream-video"
              />

              <div className="video-overlay">
                <div className="stream-stats">
                  <div className="stat viewers">
                    <FiEye />
                    <span>{stream ? stream.viewers.toLocaleString() : '1,245'} watching</span>
                  </div>
                  <div className="stream-duration">
                    <span>{streamDuration}</span>
                  </div>
                </div>

                <div className="video-controls">
                  <div className="control-group">
                    <button className="video-control-btn" onClick={toggleMute}>
                      {isMuted ? <FiVolumeX /> : <FiVolume2 />}
                    </button>
                    <div className="volume-slider">
                      <input type="range" min="0" max="100" defaultValue="80" />
                    </div>
                  </div>

                  <div className="control-group">
                    <button className="video-control-btn fullscreen" onClick={toggleFullscreen}>
                      <FiMaximize />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="stream-info-bar">
              <div className="streamer-info">
                <img
                  src={stream ? stream.userAvatar : "https://randomuser.me/api/portraits/men/32.jpg"}
                  alt={stream ? stream.name : "Alex Johnson"}
                />
                <div>
                  <h3>
                    {stream ? stream.name : "Alex Johnson"}
                    {(stream?.isVerified || true) && <span className="verified-badge">✓</span>}
                  </h3>
                  <p className="stream-category">{stream ? stream.category : "Music"}</p>
                </div>
              </div>

              <div className="stream-actions">
                <button
                  className={`follow-btn btn ${isFollowing ? 'btn-outline' : 'btn-primary'} btn-sm`}
                  onClick={handleFollowClick}
                >
                  {isFollowing ? 'Following' : 'Follow'}
                </button>
                <button className="action-btn btn btn-icon btn-secondary" onClick={shareStream}>
                  <FiShare2 />
                </button>
              </div>
            </div>

            <div className="interaction-bar">
              <button
                className={`interaction-btn btn ${isStreamLiked ? 'liked' : ''}`}
                onClick={handleLikeClick}
              >
                <div className="action-icon">
                  <FiHeart size={22} />
                </div>
                <span>{streamLikes.toLocaleString()}</span>
              </button>

              <button className="interaction-btn btn">
                <div className="action-icon">
                  <FiMessageSquare size={22} />
                </div>
                <span>Comment</span>
              </button>

              <button className="interaction-btn btn" onClick={shareStream}>
                <div className="action-icon share-icon">
                  <FiShare2 size={22} />
                </div>
                <span>Share</span>
              </button>

              <button
                className={`interaction-btn btn ${isSaved ? 'saved' : ''}`}
                onClick={handleSaveClick}
              >
                <div className="action-icon">
                  <FiBookmark size={22} />
                </div>
                <span>{isSaved ? 'Saved' : 'Save'}</span>
              </button>

              <button className="interaction-btn gift btn">
                <div className="action-icon gift-icon">
                  <FiGift size={22} />
                </div>
                <span>Gift</span>
              </button>

              <button className="interaction-btn btn">
                <div className="action-icon">
                  <FiMoreHorizontal size={22} />
                </div>
              </button>
            </div>

            <div className="stream-description">
              <h3>About this stream</h3>
              <p>
                {stream ? stream.description || 'Join me for an amazing live session!' :
                'Join me for an amazing live music session! I\'ll be playing some of my favorite songs and taking requests from the audience. Feel free to chat and let me know what you\'d like to hear!'}
              </p>

              <div className="stream-tags">
                {(stream?.tags || ['music', 'live', 'guitar', 'acoustic']).map(tag => (
                  <span key={tag} className="stream-tag">#{tag}</span>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="stream-chat">
          <div className="chat-header">
            <h3>
              <FiMessageSquare />
              Live Chat
            </h3>
            <div className="chat-viewers">
              <FiUsers />
              <span>{stream ? stream.viewers.toLocaleString() : '1,245'}</span>
            </div>
          </div>

          <div className="chat-messages" ref={chatContainerRef}>
            {chatMessages.map(message => (
              <div key={message.id} className={`chat-message ${message.isGift ? 'gift' : ''}`}>
                <img src={message.avatar} alt={message.user} />
                <div className="message-content">
                  <div className="message-header">
                    <h4>{message.user}</h4>
                    <span className="message-time">{message.time}</span>
                  </div>
                  <p>{message.message}</p>
                </div>
              </div>
            ))}
          </div>

          <form className="chat-input" onSubmit={handleSendMessage}>
            <input
              type="text"
              placeholder="Add a comment..."
              value={newChatMessage}
              onChange={(e) => setNewChatMessage(e.target.value)}
            />
            <button
              type="submit"
              className="send-btn btn btn-primary btn-sm"
              disabled={!newChatMessage.trim()}
            >
              Send
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LiveStreamView;
