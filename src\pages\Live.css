.live-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.live-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-wrapper {
  position: relative;
  margin-right: 10px;
}

.search-toggle-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.dark .search-toggle-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.search-toggle-btn:hover {
  background-color: var(--light-gray);
  transform: scale(1.05);
}

.dark .search-toggle-btn:hover {
  background-color: #3a3a3a;
}

.live-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
}

.go-live-btn {
  background-color: #FF3366 !important;
  display: flex;
  align-items: center;
  gap: 8px;
}

.go-live-btn:hover {
  background-color: #e62e5c !important;
}

.live-content-wrapper {
  display: flex;
  gap: 20px;
}

.live-main-content {
  flex: 1;
}

.live-sidebar {
  width: 300px;
  flex-shrink: 0;
}

.live-sort-options {
  background-color: var(--white);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.dark .live-sort-options {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.live-sort-options h3 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--text-color);
}

.dark .live-sort-options h3 {
  color: var(--white);
}

.sort-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 15px;
  border-radius: 10px;
  background-color: var(--gray);
  border: none;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
}

.dark .sort-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.sort-btn:hover {
  background-color: var(--light-gray);
  transform: translateX(5px);
}

.dark .sort-btn:hover {
  background-color: #3a3a3a;
}

.sort-btn.active {
  background-color: #6C13B3;
  color: var(--white);
}

.dark .sort-btn.active {
  background-color: #9B4BDE;
}

.live-tips {
  background-color: var(--white);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.dark .live-tips {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.live-tips h3 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--text-color);
}

.dark .live-tips h3 {
  color: var(--white);
}

.tips-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tips-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--light-gray);
}

.dark .tips-list li {
  border-bottom-color: #2a2a2a;
}

.tips-list li:last-child {
  border-bottom: none;
}

.tips-list li svg {
  color: #6C13B3;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.dark .tips-list li svg {
  color: #9B4BDE;
}

.tips-list li span {
  font-size: 0.95rem;
  color: var(--text-color);
}

.dark .tips-list li span {
  color: var(--white);
}

.go-live-btn::before {
  content: '';
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: var(--white);
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(255, 255, 255, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

/* Featured Streams */
.featured-streams {
  margin-bottom: 40px;
}

.featured-streams h2 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.featured-stream-card {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-decoration: none;
  color: var(--text-color);
  transition: transform 0.2s, box-shadow 0.2s;
}

.dark .featured-stream-card {
  background-color: #1e1e1e;
  color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.featured-stream-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark .featured-stream-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.stream-thumbnail {
  position: relative;
  aspect-ratio: 16/9;
}

.stream-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.live-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #FF3366;
  color: var(--white);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 700;
}

.live-badge.large {
  padding: 5px 10px;
  font-size: 0.9rem;
}

.stream-viewers {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--white);
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.stream-info {
  padding: 15px;
}

.stream-creator {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.stream-creator img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.stream-creator h3 {
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.8rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.stream-category {
  font-size: 0.85rem;
  color: var(--dark-gray);
  text-transform: capitalize;
}

.dark .stream-category {
  color: #aaa;
}

.stream-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.stream-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stream-tag {
  font-size: 0.85rem;
  color: #6C13B3;
  font-weight: 600;
}

.dark .stream-tag {
  color: #9B4BDE;
}

/* Live Categories */
.live-categories {
  margin-bottom: 30px;
}

.categories-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.categories-header h2 {
  font-size: 1.3rem;
  font-weight: 700;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  /* Other styles are handled by global button classes */
}

.category-tabs {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding-bottom: 10px;
}

.category-tab {
  white-space: nowrap;
  /* Other styles are handled by global button classes */
}

/* Live Streams Grid */
.live-streams-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stream-card {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-decoration: none;
  color: var(--text-color);
  transition: transform 0.2s, box-shadow 0.2s;
}

.dark .stream-card {
  background-color: #1e1e1e;
  color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.stream-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark .stream-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.stream-category-tag {
  display: inline-block;
  padding: 4px 8px;
  background-color: rgba(108, 19, 179, 0.1);
  color: #6C13B3;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

.dark .stream-category-tag {
  background-color: rgba(155, 75, 222, 0.2);
  color: #9B4BDE;
}

/* Live Stream View */
.live-stream-view {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--white);
  z-index: 100;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dark .live-stream-view {
  background-color: #121212;
}

.stream-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .stream-header {
  border-bottom-color: #2a2a2a;
}

.stream-title-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stream-title-info h2 {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-color);
}

.dark .stream-title-info h2 {
  color: var(--white);
}

.close-stream-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.dark .close-stream-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.close-stream-btn:hover {
  background-color: var(--light-gray);
  transform: scale(1.1);
}

.dark .close-stream-btn:hover {
  background-color: #3a3a3a;
}

.stream-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.stream-player {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  overflow-y: auto;
}

.player-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  background-color: #000;
  aspect-ratio: 16/9;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark .video-container {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.stream-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 15px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5) 0%, transparent 30%, transparent 70%, rgba(0, 0, 0, 0.5) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-container:hover .video-overlay {
  opacity: 1;
}

.stream-stats {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.stat {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--white);
  font-size: 0.9rem;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 5px 10px;
  border-radius: 4px;
}

.stat.viewers {
  background-color: rgba(255, 51, 102, 0.8);
}

.stream-duration {
  color: var(--white);
  font-size: 0.9rem;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 5px 10px;
  border-radius: 4px;
}

.video-controls {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.video-control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.video-control-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.volume-slider {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20px;
  padding: 0 10px;
}

.volume-slider input {
  width: 100%;
  -webkit-appearance: none;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
}

.volume-slider input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
}

.stream-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stream-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--gray);
  border-radius: 12px;
}

.dark .stream-info-bar {
  background-color: #1e1e1e;
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.streamer-info img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.streamer-info h3 {
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
}

.streamer-info p {
  font-size: 0.95rem;
  color: var(--dark-gray);
}

.dark .streamer-info p {
  color: #aaa;
}

.stream-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Follow button styles are now handled by global button classes */

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dark .action-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.action-btn:hover {
  background-color: var(--light-gray);
}

.dark .action-btn:hover {
  background-color: #3a3a3a;
}

.interaction-bar {
  display: flex;
  gap: 15px;
  padding: 10px;
  background-color: var(--gray);
  border-radius: 12px;
}

.dark .interaction-bar {
  background-color: #1e1e1e;
}

.stream-description {
  padding: 20px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-top: 10px;
}

.dark .stream-description {
  background-color: #1e1e1e;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.stream-description h3 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: var(--text-color);
}

.dark .stream-description h3 {
  color: var(--white);
}

.stream-description p {
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 15px;
  color: var(--text-color);
}

.dark .stream-description p {
  color: var(--white);
}

.stream-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stream-tag {
  font-size: 0.9rem;
  color: #6C13B3;
  font-weight: 600;
  transition: all 0.2s ease;
}

.dark .stream-tag {
  color: #9B4BDE;
}

.stream-tag:hover {
  transform: scale(1.05);
  opacity: 0.8;
}

.interaction-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  /* Other styles are handled by global button classes */
}

.interaction-btn.gift {
  color: #FF3366 !important;
  border-color: #FF3366 !important;
}

.interaction-btn.gift:hover {
  background-color: rgba(255, 51, 102, 0.1) !important;
}

/* Stream Chat */
.stream-chat {
  width: 300px;
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.dark .stream-chat {
  background-color: #1e1e1e;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .chat-header {
  border-bottom-color: #2a2a2a;
}

.chat-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
}

.dark .chat-header h3 {
  color: var(--white);
}

.chat-viewers {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--dark-gray);
  font-size: 0.9rem;
}

.dark .chat-viewers {
  color: #aaa;
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-message {
  display: flex;
  gap: 10px;
}

.chat-message img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
}

.message-content h4 {
  font-size: 0.9rem;
  font-weight: 600;
}

.message-time {
  font-size: 0.75rem;
  color: var(--dark-gray);
}

.dark .message-time {
  color: #aaa;
}

.message-content p {
  font-size: 0.9rem;
}

.chat-message.gift .message-content p {
  color: #FF3366;
  font-weight: 600;
}

.chat-input {
  display: flex;
  padding: 15px;
  border-top: 1px solid var(--light-gray);
}

.dark .chat-input {
  border-top-color: #2a2a2a;
}

.chat-input input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid var(--light-gray);
  border-radius: 20px;
  font-size: 0.95rem;
  background-color: var(--gray);
}

.dark .chat-input input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.chat-input input:focus {
  outline: none;
  border-color: #6C13B3;
}

.dark .chat-input input:focus {
  border-color: #9B4BDE;
}

.send-btn {
  margin-left: 10px;
  /* Other styles are handled by global button classes */
}

/* For mobile devices */
@media (max-width: 768px) {
  .live-container {
    padding: 15px;
  }

  .featured-grid {
    grid-template-columns: 1fr;
  }

  .live-streams-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .live-stream-view {
    flex-direction: column;
  }

  .live-stream-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
  }

  .stream-content {
    flex-direction: column;
  }

  .stream-player {
    padding: 10px;
  }

  .stream-chat {
    width: 100%;
    height: 350px;
    border-left: none;
    border-top: 1px solid var(--light-gray);
    background-color: var(--white);
  }

  .dark .stream-chat {
    background-color: #1e1e1e;
  }

  @media (min-width: 769px) {
    .stream-chat {
      width: 350px;
      height: auto;
      border-left: 1px solid var(--light-gray);
      border-top: none;
    }
  }

  .dark .stream-chat {
    border-top-color: #2a2a2a;
  }

  .video-overlay {
    opacity: 1;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7) 0%, transparent 30%, transparent 70%, rgba(0, 0, 0, 0.7) 100%);
  }

  .volume-slider {
    display: none;
  }

  .live-content-wrapper {
    flex-direction: column;
  }

  .live-sidebar {
    width: 100%;
    order: -1;
    margin-bottom: 20px;
  }

  .sort-buttons {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
  }

  .sort-btn {
    flex: 1;
    min-width: 120px;
  }

  .sort-btn:hover {
    transform: translateY(-3px);
  }

  .tips-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
  }

  .tips-list li {
    border-bottom: none;
    background-color: var(--gray);
    padding: 12px;
    border-radius: 10px;
  }

  .dark .tips-list li {
    background-color: #2a2a2a;
  }
}
