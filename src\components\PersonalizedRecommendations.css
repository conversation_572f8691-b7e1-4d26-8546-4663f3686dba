.personalized-recommendations {
  background-color: var(--white);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.dark .personalized-recommendations {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.recommendations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.recommendations-header h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.dark .recommendations-header h3 {
  color: var(--white);
}

.recommendations-actions {
  display: flex;
  gap: 10px;
}

.feedback-btn, .refresh-btn {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--dark-gray);
  font-size: 1.1rem;
}

.dark .feedback-btn, .dark .refresh-btn {
  color: #aaa;
}

.feedback-btn:hover, .refresh-btn:hover {
  background-color: var(--gray);
  color: var(--text-color);
}

.dark .feedback-btn:hover, .dark .refresh-btn:hover {
  background-color: #2a2a2a;
  color: var(--white);
}

.like-btn:hover {
  color: #33CC33;
}

.dislike-btn:hover {
  color: #FF3366;
}

.refresh-icon {
  transition: transform 0.5s ease;
}

.refresh-icon.refreshing {
  animation: spin 1s linear;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.feedback-message {
  padding: 10px 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  font-size: 0.9rem;
  animation: fadeIn 0.3s ease;
}

.feedback-message.like {
  background-color: rgba(51, 204, 51, 0.1);
  color: #33CC33;
  border-left: 3px solid #33CC33;
}

.feedback-message.dislike {
  background-color: rgba(255, 51, 102, 0.1);
  color: #FF3366;
  border-left: 3px solid #FF3366;
}

.dark .feedback-message.like {
  background-color: rgba(51, 204, 51, 0.2);
}

.dark .feedback-message.dislike {
  background-color: rgba(255, 51, 102, 0.2);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.recommendation-categories {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 15px;
  border-radius: 12px;
  background-color: transparent;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.dark .category-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.category-icon {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

.category-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
}

.dark .category-name {
  color: var(--white);
}

.interest-topics {
  margin-top: 20px;
}

.interest-topics h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.dark .interest-topics h4 {
  color: var(--white);
}

.interest-topics p {
  font-size: 0.9rem;
  color: var(--dark-gray);
  margin-bottom: 15px;
}

.dark .interest-topics p {
  color: #aaa;
}

.topics-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.topic-btn {
  padding: 8px 15px;
  border-radius: 20px;
  background-color: var(--gray);
  border: none;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .topic-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.topic-btn.selected {
  background-color: #6C13B3;
  color: white;
}

.dark .topic-btn.selected {
  background-color: #9B4BDE;
}

.topic-btn:hover:not(.selected) {
  background-color: var(--light-gray);
}

.dark .topic-btn:hover:not(.selected) {
  background-color: #3a3a3a;
}

/* Responsive styles */
@media (max-width: 768px) {
  .recommendation-categories {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .topics-grid {
    gap: 8px;
  }
  
  .topic-btn {
    padding: 6px 12px;
    font-size: 0.85rem;
  }
}
