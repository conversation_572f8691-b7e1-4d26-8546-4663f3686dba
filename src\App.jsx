import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { ThemeProvider, ThemeContext } from './contexts/ThemeContext'
import './App.css'

// Pages
import Home from './pages/Home'
import Profile from './pages/Profile'
import Upload from './pages/Upload'
import Settings from './pages/Settings'
import Notifications from './pages/Notifications'
import Messages from './pages/Messages'
import Following from './pages/Following'
import Explore from './pages/Explore'
import ExploreHashtags from './pages/ExploreHashtags'
import LivePage from './pages/LivePage'
import NotFound from './pages/NotFound'

// Components
import Navbar from './components/Navbar'
import Sidebar from './components/Sidebar'
import SplashScreen from './components/SplashScreen'

function App() {
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    // Automatically hide splash screen after 2.5 seconds
    const timer = setTimeout(() => {
      setShowSplash(false);
    }, 2500);

    return () => clearTimeout(timer);
  }, []);

  const handleSplashFinish = () => {
    setShowSplash(false);
  };

  return (
    <ThemeProvider>
      {showSplash ? (
        <SplashScreen onFinish={handleSplashFinish} />
      ) : (
        <Router>
          <div className="app-container">
            <Navbar />
            <div className="main-container">
              <Sidebar />
              <div className="content">
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/profile/:username" element={<Profile />} />
                  <Route path="/upload" element={<Upload />} />
                  <Route path="/settings" element={<Settings />} />
                  <Route path="/notifications" element={<Notifications />} />
                  <Route path="/messages" element={<Messages />} />
                  <Route path="/following" element={<Following />} />
                  <Route path="/explore" element={<Explore />} />
                  <Route path="/explore/hashtags" element={<ExploreHashtags />} />
                  <Route path="/explore/tag/:tag" element={<ExploreHashtags />} />
                  <Route path="/live" element={<LivePage />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </div>
            </div>
          </div>
        </Router>
      )}
    </ThemeProvider>
  )
}

export default App
