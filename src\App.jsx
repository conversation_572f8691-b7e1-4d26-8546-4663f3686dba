import { useState, useEffect } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import ThemeProvider from './contexts/ThemeContext'
import './App.css'
import { ThemeContext } from './context/ThemeContext'

// Pages
import Home from './pages/Home'
import Profile from './pages/Profile'
import Upload from './pages/Upload'
import Settings from './pages/Settings'
import Notifications from './pages/Notifications'
import Messages from './pages/Messages'
import Following from './pages/Following'
import Explore from './pages/Explore'
import ExploreHashtags from './pages/ExploreHashtags'
import LivePage from './pages/LivePage'
import NotFound from './pages/NotFound'

// Components
import Navbar from './components/Navbar'
import Sidebar from './components/Sidebar'
import SplashScreen from './components/SplashScreen'

function App() {
  const [showSplash, setShowSplash] = useState(true);
  const [darkMode, setDarkMode] = useState(false);

  useEffect(() => {
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      setDarkMode(true);
      document.body.classList.add('dark-theme');
    }

    // Automatically hide splash screen after 2.5 seconds
    const timer = setTimeout(() => {
      setShowSplash(false);
    }, 2500);

    return () => clearTimeout(timer);
  }, []);

  const toggleTheme = () => {
    setDarkMode(!darkMode);
    if (!darkMode) {
      document.body.classList.add('dark-theme');
      localStorage.setItem('theme', 'dark');
    } else {
      document.body.classList.remove('dark-theme');
      localStorage.setItem('theme', 'light');
    }
  };

  const handleSplashFinish = () => {
    setShowSplash(false);
  };

  return (
    <ThemeContext.Provider value={{ darkMode, toggleTheme }}>
      {showSplash ? (
        <SplashScreen onFinish={handleSplashFinish} />
      ) : (
        <Router>
          <div className={`app-container ${darkMode ? 'dark' : ''}`}>
            <Navbar />
            <div className="main-container">
              <Sidebar />
              <div className="content">
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/profile/:username" element={<Profile />} />
                  <Route path="/upload" element={<Upload />} />
                  <Route path="/settings" element={<Settings />} />
                  <Route path="/notifications" element={<Notifications />} />
                  <Route path="/messages" element={<Messages />} />
                  <Route path="/following" element={<Following />} />
                  <Route path="/explore" element={<Explore />} />
                  <Route path="/explore/hashtags" element={<ExploreHashtags />} />
                  <Route path="/explore/tag/:tag" element={<ExploreHashtags />} />
                  <Route path="/live" element={<LivePage />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </div>
            </div>
          </div>
        </Router>
      )}
    </ThemeContext.Provider>
  )
}

export default App
