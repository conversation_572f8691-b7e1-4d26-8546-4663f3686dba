import React, { useState, useRef, useEffect } from 'react';
import { FiSearch, FiMusic, FiHash, FiVideo, FiTrendingUp, FiUsers, FiCamera, FiStar, FiCoffee, FiHeart, FiBookmark, FiShoppingBag, FiActivity, FiAward, FiGlobe, FiFilter, FiUser, FiGrid, FiCompass } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import { ScrollableContainer, BackToTopButton, SectionNavigation } from '../components/ScrollNavigation';
import Pagination from '../components/Pagination';
import './Explore.css';

const Explore = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('trending');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [recentSearches, setRecentSearches] = useState(['dance challenge', 'food recipes', 'travel vlog']);
  const [showRecentSearches, setShowRecentSearches] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(8);
  const searchInputRef = useRef(null);
  const filterRef = useRef(null);

  // Section navigation configuration
  const sections = [
    { id: 'recommended-section', label: 'Recommended', icon: <FiStar /> },
    { id: 'hashtags-section', label: 'Hashtags', icon: <FiHash /> },
    { id: 'videos-section', label: 'Videos', icon: <FiVideo /> },
    { id: 'creators-section', label: 'Creators', icon: <FiUser /> },
    { id: 'categories-section', label: 'Categories', icon: <FiGrid /> }
  ];

  // Mock categories
  const categories = [
    { id: 'for-you', name: 'For You', icon: <FiStar /> },
    { id: 'trending', name: 'Trending', icon: <FiTrendingUp /> },
    { id: 'music', name: 'Music', icon: <FiMusic /> },
    { id: 'dance', name: 'Dance', icon: <FiUsers /> },
    { id: 'comedy', name: 'Comedy', icon: <FiVideo /> },
    { id: 'food', name: 'Food', icon: <FiCoffee /> },
    { id: 'travel', name: 'Travel', icon: <FiGlobe /> },
    { id: 'fashion', name: 'Fashion', icon: <FiShoppingBag /> },
    { id: 'fitness', name: 'Fitness', icon: <FiActivity /> },
    { id: 'pets', name: 'Pets', icon: <FiHeart /> },
    { id: 'gaming', name: 'Gaming', icon: <FiAward /> }
  ];

  // Filter options
  const filterOptions = [
    { id: 'popular', name: 'Most Popular' },
    { id: 'recent', name: 'Most Recent' },
    { id: 'liked', name: 'Most Liked' },
    { id: 'commented', name: 'Most Commented' },
    { id: 'shared', name: 'Most Shared' },
    { id: 'saved', name: 'Most Saved' }
  ];

  // Mock trending hashtags
  const trendingHashtags = [
    { tag: 'vibevid', count: '2.5B', color: '#FF3366' },
    { tag: 'dance', count: '1.8B', color: '#00CCFF' },
    { tag: 'music', count: '1.2B', color: '#6C13B3' },
    { tag: 'comedy', count: '950M', color: '#FF9900' },
    { tag: 'food', count: '780M', color: '#33CC33' },
    { tag: 'travel', count: '650M', color: '#3366FF' },
    { tag: 'pets', count: '520M', color: '#FF6600' },
    { tag: 'fitness', count: '480M', color: '#9933CC' }
  ];

  // Mock trending videos
  const trendingVideos = [
    {
      id: 1,
      thumbnail: 'https://placehold.co/300x400/FF3366/FFFFFF?text=Dance+Challenge',
      title: 'New Dance Challenge',
      views: '1.2M',
      username: 'emma_wilson',
      userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    {
      id: 2,
      thumbnail: 'https://placehold.co/300x400/00CCFF/FFFFFF?text=City+Lights',
      title: 'City Lights Tour',
      views: '456K',
      username: 'sophia_garcia',
      userAvatar: 'https://randomuser.me/api/portraits/women/56.jpg'
    },
    {
      id: 3,
      thumbnail: 'https://placehold.co/300x400/6C13B3/FFFFFF?text=Music+Cover',
      title: 'Amazing Song Cover',
      views: '789K',
      username: 'alex_johnson',
      userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    {
      id: 4,
      thumbnail: 'https://placehold.co/300x400/FF9900/FFFFFF?text=Food+Recipe',
      title: 'Easy 5-Minute Recipe',
      views: '123K',
      username: 'daniel_lee',
      userAvatar: 'https://randomuser.me/api/portraits/men/45.jpg'
    },
    {
      id: 5,
      thumbnail: 'https://placehold.co/300x400/33CC33/FFFFFF?text=Nature+Walk',
      title: 'Beautiful Nature Walk',
      views: '45K',
      username: 'michael_brown',
      userAvatar: 'https://randomuser.me/api/portraits/men/22.jpg'
    },
    {
      id: 6,
      thumbnail: 'https://placehold.co/300x400/3366FF/FFFFFF?text=Travel+Vlog',
      title: 'Amazing Travel Vlog',
      views: '6.7K',
      username: 'sophia_garcia',
      userAvatar: 'https://randomuser.me/api/portraits/women/56.jpg'
    },
    {
      id: 7,
      thumbnail: 'https://placehold.co/300x400/FF6600/FFFFFF?text=Cute+Pets',
      title: 'Cutest Pets Compilation',
      views: '89K',
      username: 'michael_brown',
      userAvatar: 'https://randomuser.me/api/portraits/men/22.jpg'
    },
    {
      id: 8,
      thumbnail: 'https://placehold.co/300x400/9933CC/FFFFFF?text=Workout+Tips',
      title: 'Quick Workout Tips',
      views: '12K',
      username: 'emma_wilson',
      userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    }
  ];

  // Mock trending creators
  const trendingCreators = [
    {
      username: 'emma_wilson',
      name: 'Emma Wilson',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      followers: '2.5M',
      isVerified: true
    },
    {
      username: 'alex_johnson',
      name: 'Alex Johnson',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      followers: '1.8M',
      isVerified: true
    },
    {
      username: 'sophia_garcia',
      name: 'Sophia Garcia',
      avatar: 'https://randomuser.me/api/portraits/women/56.jpg',
      followers: '1.2M',
      isVerified: true
    },
    {
      username: 'michael_brown',
      name: 'Michael Brown',
      avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      followers: '950K',
      isVerified: false
    },
    {
      username: 'daniel_lee',
      name: 'Daniel Lee',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      followers: '780K',
      isVerified: false
    }
  ];

  // Mock For You videos
  const forYouVideos = [
    {
      id: 101,
      thumbnail: 'https://placehold.co/300x400/FF3366/FFFFFF?text=Recommended+1',
      title: 'Top 10 Dance Moves',
      views: '2.3M',
      username: 'emma_wilson',
      userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      isRecommended: true
    },
    {
      id: 102,
      thumbnail: 'https://placehold.co/300x400/00CCFF/FFFFFF?text=Recommended+2',
      title: 'Easy Cooking Hacks',
      views: '1.7M',
      username: 'daniel_lee',
      userAvatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      isRecommended: true
    },
    {
      id: 103,
      thumbnail: 'https://placehold.co/300x400/6C13B3/FFFFFF?text=Recommended+3',
      title: 'Morning Routine Tips',
      views: '890K',
      username: 'sophia_garcia',
      userAvatar: 'https://randomuser.me/api/portraits/women/56.jpg',
      isRecommended: true
    },
    {
      id: 104,
      thumbnail: 'https://placehold.co/300x400/FF9900/FFFFFF?text=Recommended+4',
      title: 'Weekend Travel Ideas',
      views: '567K',
      username: 'michael_brown',
      userAvatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      isRecommended: true
    }
  ];

  // Close filters when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (filterRef.current && !filterRef.current.contains(event.target)) {
        setShowFilters(false);
      }
      if (searchInputRef.current && !searchInputRef.current.contains(event.target) && !event.target.closest('.recent-searches')) {
        setShowRecentSearches(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // In a real app, this would trigger a search API call
      console.log('Searching for:', searchQuery);

      // Add to recent searches if not already there
      if (!recentSearches.includes(searchQuery.trim())) {
        setRecentSearches([searchQuery.trim(), ...recentSearches.slice(0, 4)]);
      }

      setShowRecentSearches(false);
    }
  };

  const handleSearchInputFocus = () => {
    setShowRecentSearches(true);
  };

  const handleRecentSearchClick = (search) => {
    setSearchQuery(search);
    setShowRecentSearches(false);
    // In a real app, this would trigger a search API call
    console.log('Searching for:', search);
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
  };

  const toggleFilter = (filterId) => {
    setSelectedFilters(prev => {
      if (prev.includes(filterId)) {
        return prev.filter(id => id !== filterId);
      } else {
        return [...prev, filterId];
      }
    });
  };

  // Pagination logic
  const getPaginatedItems = (items) => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return items.slice(startIndex, startIndex + itemsPerPage);
  };

  const handlePageChange = (newPage) => {
    setCurrentPage(newPage);
    // Scroll to the top of the content section
    const contentSection = document.querySelector('.explore-content');
    if (contentSection) {
      contentSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Get total pages for current category
  const getTotalPages = () => {
    let totalItems;
    if (activeCategory === 'for-you') {
      totalItems = forYouVideos.length;
    } else {
      totalItems = trendingVideos.length;
    }
    return Math.ceil(totalItems / itemsPerPage);
  };

  // Reset pagination when category changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeCategory]);

  const focusSearch = () => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  return (
    <div className="explore-container">
      <div className="explore-header">
        <h1>Explore</h1>
        <div className="search-container" ref={searchInputRef}>
          <form className="explore-search" onSubmit={handleSearch}>
            <FiSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search videos, users, or hashtags"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={handleSearchInputFocus}
            />
            <button type="submit" className="search-btn btn btn-primary btn-sm">Search</button>
          </form>

          {showRecentSearches && recentSearches.length > 0 && (
            <div className="recent-searches">
              <div className="recent-searches-header">
                <h3>Recent Searches</h3>
                <button className="clear-btn" onClick={clearRecentSearches}>Clear All</button>
              </div>
              <ul className="recent-search-list">
                {recentSearches.map((search, index) => (
                  <li key={index} onClick={() => handleRecentSearchClick(search)}>
                    <FiSearch size={14} />
                    <span>{search}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>

        <div className="filter-container" ref={filterRef}>
          <button
            className={`filter-toggle-btn ${showFilters ? 'active' : ''}`}
            onClick={() => setShowFilters(!showFilters)}
          >
            <FiFilter />
            <span>Filters</span>
          </button>

          {showFilters && (
            <div className="filter-dropdown">
              <h3>Sort By</h3>
              <div className="filter-options">
                {filterOptions.map(filter => (
                  <div className="filter-option" key={filter.id}>
                    <label>
                      <input
                        type="checkbox"
                        checked={selectedFilters.includes(filter.id)}
                        onChange={() => toggleFilter(filter.id)}
                      />
                      <span>{filter.name}</span>
                    </label>
                  </div>
                ))}
              </div>
              <div className="filter-actions">
                <button className="clear-filters-btn" onClick={() => setSelectedFilters([])}>Clear</button>
                <button className="apply-filters-btn" onClick={() => setShowFilters(false)}>Apply</button>
              </div>
            </div>
          )}
        </div>
      </div>

      <ScrollableContainer className="explore-categories-wrapper">
        <div className="explore-categories">
          {categories.map(category => (
            <button
              key={category.id}
              className={`category-btn btn ${activeCategory === category.id ? 'btn-primary' : 'btn-secondary'}`}
              onClick={() => setActiveCategory(category.id)}
            >
              {category.icon}
              <span>{category.name}</span>
            </button>
          ))}
        </div>
      </ScrollableContainer>

      <div className="explore-content">
        {activeCategory === 'for-you' && (
          <div id="recommended-section" className="explore-section">
            <div className="section-header">
              <h2>Recommended For You</h2>
              <div className="section-actions">
                <span className="recommendation-info">Based on your activity</span>
                <Link to="/explore/for-you" className="see-all-link">See All</Link>
              </div>
            </div>
            <div className="videos-grid">
              {forYouVideos.map(video => (
                <Link key={video.id} to={`/video/${video.id}`} className="video-card recommended">
                  <div className="video-thumbnail">
                    <img src={video.thumbnail} alt={video.title} />
                    <div className="video-views">{video.views} views</div>
                    {video.isRecommended && <div className="recommended-badge">Recommended</div>}
                  </div>
                  <div className="video-info">
                    <h3>{video.title}</h3>
                    <div className="video-creator">
                      <img src={video.userAvatar} alt={video.username} />
                      <span>@{video.username}</span>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}

        <div id="hashtags-section" className="explore-section">
          <div className="section-header">
            <h2>Trending Hashtags</h2>
            <Link to="/explore/hashtags" className="see-all-link">See All</Link>
          </div>
          <ScrollableContainer className="hashtags-scroll">
            <div className="hashtags-grid">
              {trendingHashtags.map(hashtag => (
                <Link
                  key={hashtag.tag}
                  to={`/explore/tag/${hashtag.tag}`}
                  className="hashtag-card"
                  style={{ backgroundColor: `${hashtag.color}20` }}
                >
                  <div className="hashtag-icon" style={{ backgroundColor: hashtag.color }}>
                    <FiHash color="white" />
                  </div>
                  <div className="hashtag-info">
                    <h3 style={{ color: hashtag.color }}>#{hashtag.tag}</h3>
                    <p>{hashtag.count} views</p>
                  </div>
                </Link>
              ))}
            </div>
          </ScrollableContainer>
        </div>

        <div id="videos-section" className="explore-section">
          <div className="section-header">
            <h2>{activeCategory === 'for-you' ? 'Popular Videos' : 'Trending Videos'}</h2>
            <Link to="/explore/videos" className="see-all-link">See All</Link>
          </div>
          <div className="videos-grid">
            {getPaginatedItems(trendingVideos).map(video => (
              <Link key={video.id} to={`/video/${video.id}`} className="video-card">
                <div className="video-thumbnail">
                  <img src={video.thumbnail} alt={video.title} />
                  <div className="video-views">{video.views} views</div>
                </div>
                <div className="video-info">
                  <h3>{video.title}</h3>
                  <div className="video-creator">
                    <img src={video.userAvatar} alt={video.username} />
                    <span>@{video.username}</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {trendingVideos.length > itemsPerPage && (
            <Pagination
              currentPage={currentPage}
              totalPages={getTotalPages()}
              onPageChange={handlePageChange}
            />
          )}
        </div>

        <div id="creators-section" className="explore-section">
          <div className="section-header">
            <h2>Trending Creators</h2>
            <Link to="/explore/creators" className="see-all-link">See All</Link>
          </div>
          <ScrollableContainer className="creators-scroll">
            <div className="creators-grid">
              {trendingCreators.map(creator => (
                <Link key={creator.username} to={`/profile/${creator.username}`} className="creator-card">
                  <div className="creator-avatar">
                    <img src={creator.avatar} alt={creator.name} />
                  </div>
                  <div className="creator-info">
                    <h3>
                      {creator.name}
                      {creator.isVerified && <span className="verified-badge">✓</span>}
                    </h3>
                    <p>@{creator.username}</p>
                    <span className="creator-followers">{creator.followers} followers</span>
                  </div>
                  <button className="follow-btn btn btn-primary btn-sm">Follow</button>
                </Link>
              ))}
            </div>
          </ScrollableContainer>
        </div>

        <div id="categories-section" className="explore-section">
          <div className="section-header">
            <h2>Discover by Category</h2>
          </div>
          <div className="category-discovery-grid">
            {categories.slice(2).map(category => (
              <Link key={category.id} to={`/explore/category/${category.id}`} className="category-discovery-card">
                <div className="category-icon">
                  {category.icon}
                </div>
                <h3>{category.name}</h3>
                <p>Explore {category.name.toLowerCase()} content</p>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <SectionNavigation sections={sections} />

      {/* Back to Top Button */}
      <BackToTopButton />

      <div className="explore-search-overlay" onClick={focusSearch}>
        <div className="search-prompt">
          <FiSearch size={24} />
          <p>Tap to search</p>
        </div>
      </div>
    </div>
  );
};

export default Explore;
