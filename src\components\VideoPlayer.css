.video-player-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background-color: #000;
  border-radius: 12px;
  overflow: hidden;
}

.video-player-element {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  z-index: 2;
}

.play-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(108, 19, 179, 0.8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.play-button:hover {
  background-color: rgba(108, 19, 179, 1);
  transform: scale(1.1);
}

.video-controls-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  padding: 20px 15px 10px;
  z-index: 3;
  transition: opacity 0.3s ease;
}

.progress-container {
  width: 100%;
  height: 20px;
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  margin-bottom: 10px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  transition: height 0.2s ease;
}

.progress-container:hover .progress-bar {
  height: 6px;
}

.progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #6C13B3;
  border-radius: 2px;
}

.thumbnail-preview {
  position: absolute;
  bottom: 25px;
  transform: translateX(-50%);
  width: 160px;
  height: 90px;
  background-size: cover;
  background-position: center;
  border-radius: 4px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
  z-index: 4;
}

.thumbnail-time {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 0.8rem;
  padding: 4px 8px;
  text-align: center;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-controls, .right-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  position: relative;
}

.control-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.control-tooltip {
  position: absolute;
  bottom: 45px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.control-button:hover .control-tooltip {
  opacity: 1;
  visibility: visible;
}

.time-display {
  color: white;
  font-size: 0.9rem;
  display: flex;
  gap: 5px;
}

.volume-container {
  position: relative;
  display: flex;
  align-items: center;
}

.volume-slider-container {
  position: absolute;
  bottom: 45px;
  left: 50%;
  transform: translateX(-50%) rotate(-90deg);
  width: 100px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  transform-origin: bottom center;
}

.volume-slider {
  width: 100%;
  -webkit-appearance: none;
  appearance: none;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #6C13B3;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #6C13B3;
  cursor: pointer;
  border: none;
}

.settings-container {
  position: relative;
}

.settings-menu {
  position: absolute;
  bottom: 45px;
  right: 0;
  width: 200px;
  background-color: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  padding: 15px;
  z-index: 5;
}

.settings-section h4 {
  color: white;
  font-size: 0.9rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.playback-rates {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.playback-rate-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.playback-rate-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.playback-rate-btn.active {
  background-color: #6C13B3;
}

.buffering-indicator {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 4;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #6C13B3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.video-player-container.fullscreen {
  padding-bottom: 0;
  height: 100%;
}

/* Responsive styles */
@media (max-width: 768px) {
  .play-button {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .video-controls-container {
    padding: 15px 10px 8px;
  }
  
  .control-button {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .time-display {
    font-size: 0.8rem;
  }
  
  .thumbnail-preview {
    width: 120px;
    height: 68px;
  }
}

@media (max-width: 480px) {
  .right-controls {
    gap: 5px;
  }
  
  .control-button {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }
  
  .time-display {
    font-size: 0.7rem;
  }
  
  .settings-menu {
    width: 180px;
    right: -70px;
  }
  
  .settings-menu::after {
    content: '';
    position: absolute;
    bottom: -8px;
    right: 80px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid rgba(0, 0, 0, 0.9);
  }
}
