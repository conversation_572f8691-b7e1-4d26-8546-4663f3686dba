<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok Clone</title>
    <link rel="icon" href="tiktok-icon.svg">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #000;
            color: #fff;
        }
        
        .splash-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .splash-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        .logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .tiktok-logo {
            position: relative;
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }
        
        .note-1, .note-2 {
            position: absolute;
            width: 45px;
            height: 80px;
            border-radius: 20px;
        }
        
        .note-1 {
            left: 0;
            background-color: #25F4EE;
            transform: skew(20deg);
            z-index: 1;
        }
        
        .note-2 {
            right: 0;
            background-color: #FE2C55;
            transform: skew(-20deg);
            z-index: 2;
        }
        
        .note-1::before, .note-2::before {
            content: '';
            position: absolute;
            top: 10px;
            width: 10px;
            height: 10px;
            background-color: white;
            border-radius: 50%;
        }
        
        .note-1::before {
            right: 10px;
        }
        
        .note-2::before {
            left: 10px;
        }
        
        .app-name {
            color: white;
            font-size: 2rem;
            font-weight: bold;
        }
        
        .app-container {
            display: none;
            flex-direction: column;
            min-height: 100vh;
        }
        
        .app-container.visible {
            display: flex;
        }
        
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            padding: 0 20px;
            background-color: #000;
            border-bottom: 1px solid #2f2f2f;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #fff;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .tiktok-logo-small {
            position: relative;
            width: 30px;
            height: 30px;
        }
        
        .note-1-small, .note-2-small {
            position: absolute;
            width: 16px;
            height: 30px;
            border-radius: 8px;
        }
        
        .note-1-small {
            left: 0;
            background-color: #25F4EE;
            transform: skew(20deg);
        }
        
        .note-2-small {
            right: 0;
            background-color: #FE2C55;
            transform: skew(-20deg);
        }
        
        .search-form {
            display: flex;
            align-items: center;
            background-color: #1f1f1f;
            border-radius: 92px;
            padding: 0 16px;
            height: 40px;
            width: 300px;
        }
        
        .search-form input {
            flex: 1;
            border: none;
            background: transparent;
            padding: 0 10px;
            color: #fff;
            outline: none;
        }
        
        .search-btn {
            background: transparent;
            border: none;
            color: #aaa;
            cursor: pointer;
        }
        
        .upload-btn, .login-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
        }
        
        .upload-btn {
            border: 1px solid #2f2f2f;
            color: #fff;
            text-decoration: none;
        }
        
        .login-btn {
            background-color: #FE2C55;
            color: #fff;
            border: none;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            padding: 20px;
        }
        
        .sidebar {
            width: 250px;
            padding-right: 20px;
        }
        
        .sidebar-menu {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .sidebar-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            color: #fff;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .sidebar-item:hover {
            background-color: #1f1f1f;
        }
        
        .sidebar-item.active {
            color: #FE2C55;
        }
        
        .content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .video-feed {
            max-width: 500px;
            width: 100%;
        }
        
        .video-card {
            border-bottom: 1px solid #2f2f2f;
            padding: 20px 0;
        }
        
        .video-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .video-info {
            flex: 1;
        }
        
        .username {
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .caption {
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .video-container {
            width: 100%;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .video-player {
            width: 100%;
            display: block;
            background-color: #1f1f1f;
        }
        
        .video-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #fff;
            background: transparent;
            border: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- Splash Screen -->
    <div class="splash-screen" id="splash-screen">
        <div class="logo-container">
            <div class="tiktok-logo">
                <div class="note-1"></div>
                <div class="note-2"></div>
            </div>
            <h1 class="app-name">TikTok Clone</h1>
        </div>
    </div>
    
    <!-- Main App -->
    <div class="app-container" id="app-container">
        <!-- Navbar -->
        <nav class="navbar">
            <a href="#" class="logo">
                <div class="tiktok-logo-small">
                    <div class="note-1-small"></div>
                    <div class="note-2-small"></div>
                </div>
                <span>TikTok Clone</span>
            </a>
            
            <div class="search-form">
                <input type="text" placeholder="Search accounts and videos">
                <button class="search-btn">🔍</button>
            </div>
            
            <div class="navbar-right">
                <a href="#" class="upload-btn">
                    <span>+</span>
                    <span>Upload</span>
                </a>
                
                <button class="login-btn">
                    <span>Log In</span>
                </button>
            </div>
        </nav>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-menu">
                    <a href="#" class="sidebar-item active">
                        <span>🏠</span>
                        <span>For You</span>
                    </a>
                    <a href="#" class="sidebar-item">
                        <span>👥</span>
                        <span>Following</span>
                    </a>
                    <a href="#" class="sidebar-item">
                        <span>🔍</span>
                        <span>Explore</span>
                    </a>
                    <a href="#" class="sidebar-item">
                        <span>📹</span>
                        <span>LIVE</span>
                    </a>
                </div>
            </div>
            
            <!-- Content -->
            <div class="content">
                <div class="video-feed">
                    <!-- Video Card 1 -->
                    <div class="video-card">
                        <div class="video-header">
                            <img src="https://placehold.co/50" alt="User Avatar" class="user-avatar">
                            <div class="video-info">
                                <div class="username">@user1</div>
                                <div class="caption">This is my first TikTok video! #firstpost #tiktok</div>
                            </div>
                        </div>
                        
                        <div class="video-container">
                            <video class="video-player" controls>
                                <source src="https://assets.mixkit.co/videos/preview/mixkit-young-woman-waving-her-hair-in-a-pool-1229-large.mp4" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                        
                        <div class="video-actions">
                            <button class="action-btn">❤️ 1.2K</button>
                            <button class="action-btn">💬 123</button>
                            <button class="action-btn">↗️ 45</button>
                        </div>
                    </div>
                    
                    <!-- Video Card 2 -->
                    <div class="video-card">
                        <div class="video-header">
                            <img src="https://placehold.co/50" alt="User Avatar" class="user-avatar">
                            <div class="video-info">
                                <div class="username">@user2</div>
                                <div class="caption">Check out this cool dance! #dance #viral</div>
                            </div>
                        </div>
                        
                        <div class="video-container">
                            <video class="video-player" controls>
                                <source src="https://assets.mixkit.co/videos/preview/mixkit-man-dancing-under-changing-lights-1240-large.mp4" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                        
                        <div class="video-actions">
                            <button class="action-btn">❤️ 5.6K</button>
                            <button class="action-btn">💬 456</button>
                            <button class="action-btn">↗️ 78</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Show splash screen for 2.5 seconds, then fade out
        setTimeout(() => {
            document.getElementById('splash-screen').classList.add('hidden');
            document.getElementById('app-container').classList.add('visible');
        }, 2500);
    </script>
</body>
</html>
