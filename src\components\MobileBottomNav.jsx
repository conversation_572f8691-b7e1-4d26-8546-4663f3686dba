import React, { useContext } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FiHome, FiUsers, FiCompass, FiRadio, FiUser, FiPlus } from 'react-icons/fi';
import { ThemeContext } from '../contexts/ThemeContext';
import './MobileBottomNav.css';

function MobileBottomNav() {
  const { isDarkMode } = useContext(ThemeContext);
  const location = useLocation();

  const navItems = [
    { path: '/', icon: FiHome, label: 'Home' },
    { path: '/following', icon: FiUsers, label: 'Following' },
    { path: '/upload', icon: FiPlus, label: 'Upload', isUpload: true },
    { path: '/explore', icon: FiCompass, label: 'Explore' },
    { path: '/profile/user123', icon: FiUser, label: 'Profile' },
  ];

  return (
    <div className={`mobile-bottom-nav ${isDarkMode ? 'dark' : ''}`}>
      {navItems.map((item) => {
        const Icon = item.icon;
        const isActive = location.pathname === item.path;
        
        return (
          <Link
            key={item.path}
            to={item.path}
            className={`bottom-nav-item ${isActive ? 'active' : ''} ${item.isUpload ? 'upload-item' : ''}`}
          >
            <div className="nav-icon">
              <Icon size={item.isUpload ? 24 : 20} />
            </div>
            <span className="nav-label">{item.label}</span>
          </Link>
        );
      })}
    </div>
  );
}

export default MobileBottomNav;
