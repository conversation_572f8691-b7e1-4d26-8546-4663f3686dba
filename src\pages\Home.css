.home-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

.home-layout {
  display: flex;
  min-height: calc(100vh - 60px);
}

/* Home Sidebar */
.home-sidebar {
  width: 320px;
  padding: 20px;
  border-right: 1px solid var(--light-gray);
  overflow-y: auto;
  height: calc(100vh - 60px);
  position: sticky;
  top: 60px;
}

.dark .home-sidebar {
  border-right-color: #2a2a2a;
}

.home-tabs {
  display: flex;
  margin-bottom: 30px;
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: 15px;
}

.dark .home-tabs {
  border-bottom-color: #2a2a2a;
}

.home-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px;
  font-weight: 600;
  font-size: 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--dark-gray);
  transition: color 0.2s;
}

.dark .home-tab {
  color: #aaa;
}

.home-tab:hover {
  color: var(--text-color);
}

.dark .home-tab:hover {
  color: var(--white);
}

.home-tab.active {
  color: #6C13B3;
}

.dark .home-tab.active {
  color: #9B4BDE;
}

.sidebar-section {
  margin-bottom: 30px;
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 15px;
}

/* Trending Hashtags */
.trending-hashtags {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trending-hashtag {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
  cursor: pointer;
}

.trending-hashtag:hover {
  background-color: var(--gray);
}

.dark .trending-hashtag:hover {
  background-color: #2a2a2a;
}

.hashtag-info {
  display: flex;
  flex-direction: column;
}

.hashtag-name {
  font-weight: 600;
  font-size: 0.95rem;
}

.hashtag-count {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .hashtag-count {
  color: #aaa;
}

/* Suggested Accounts */
.suggested-accounts {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.suggested-account {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.suggested-account:hover {
  background-color: var(--gray);
}

.dark .suggested-account:hover {
  background-color: #2a2a2a;
}

.account-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.account-info {
  flex: 1;
  min-width: 0;
}

.account-name {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 600;
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.8rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.account-username {
  font-size: 0.85rem;
  color: var(--dark-gray);
}

.dark .account-username {
  color: #aaa;
}

.suggested-account .follow-btn {
  padding: 6px 12px;
  background-color: transparent;
  border: 1px solid #6C13B3;
  color: #6C13B3;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.dark .suggested-account .follow-btn {
  border-color: #9B4BDE;
  color: #9B4BDE;
}

.suggested-account .follow-btn:hover {
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .suggested-account .follow-btn:hover {
  background-color: rgba(155, 75, 222, 0.2);
}

/* Popular Music */
.popular-music {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.music-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
  cursor: pointer;
}

.music-item:hover {
  background-color: var(--gray);
}

.dark .music-item:hover {
  background-color: #2a2a2a;
}

.music-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6C13B3;
}

.dark .music-icon {
  background-color: #2a2a2a;
  color: #9B4BDE;
}

.music-info {
  display: flex;
  flex-direction: column;
}

.music-name {
  font-weight: 600;
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.music-usage {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .music-usage {
  color: #aaa;
}

/* Sidebar Footer */
.sidebar-footer {
  padding-top: 15px;
  border-top: 1px solid var(--light-gray);
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .sidebar-footer {
  border-top-color: #2a2a2a;
  color: #aaa;
}

.footer-links {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.footer-links a {
  color: var(--dark-gray);
  text-decoration: none;
}

.dark .footer-links a {
  color: #aaa;
}

.footer-links a:hover {
  text-decoration: underline;
}

/* Video Feed */
.video-feed {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .feed-header {
  border-bottom-color: #2a2a2a;
}

.feed-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
}

.feed-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.autoplay-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.autoplay-toggle input {
  display: none;
}

.toggle-slider {
  position: relative;
  width: 40px;
  height: 20px;
  background-color: var(--light-gray);
  border-radius: 20px;
  transition: background-color 0.2s;
}

.dark .toggle-slider {
  background-color: #2a2a2a;
}

.toggle-slider:before {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--white);
  top: 2px;
  left: 2px;
  transition: transform 0.2s;
}

.dark .toggle-slider:before {
  background-color: #aaa;
}

.autoplay-toggle input:checked + .toggle-slider {
  background-color: #6C13B3;
}

.dark .autoplay-toggle input:checked + .toggle-slider {
  background-color: #9B4BDE;
}

.autoplay-toggle input:checked + .toggle-slider:before {
  transform: translateX(20px);
  background-color: var(--white);
}

/* For mobile devices */
@media (max-width: 768px) {
  .home-layout {
    flex-direction: column;
  }

  .home-sidebar {
    width: 100%;
    height: auto;
    position: static;
    border-right: none;
    border-bottom: 1px solid var(--light-gray);
    padding: 15px;
  }

  .dark .home-sidebar {
    border-bottom-color: #2a2a2a;
  }

  .home-tabs {
    margin-bottom: 20px;
  }

  .sidebar-section {
    margin-bottom: 20px;
  }

  .video-feed {
    padding: 15px;
  }
}
