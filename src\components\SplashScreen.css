.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #6C13B3;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 1;
  transition: opacity 0.5s ease-out;
}

.splash-screen.fade-out {
  opacity: 0;
}

.splash-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo-container {
  margin-bottom: 20px;
}

.vibevid-logo {
  position: relative;
  width: 120px;
  height: 120px;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.wave-1, .wave-2 {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  opacity: 0.7;
}

.wave-1 {
  background-color: #FF3366;
  clip-path: path('M 60,0 C 60,33.33 60,66.67 60,100 C 60,110 50,120 40,120 C 30,120 20,110 20,100 C 20,90 30,80 40,80 C 43.33,80 46.67,81 50,83 L 50,50 C 33.33,50 16.67,66.67 16.67,83.33 C 16.67,100 33.33,116.67 50,116.67 C 66.67,116.67 83.33,100 83.33,83.33 L 83.33,33.33 C 90,40 100,43.33 110,43.33 L 110,20 C 96.67,20 86.67,13.33 80,3.33 L 60,0 Z');
  animation: wave1 3s infinite ease-in-out;
}

.wave-2 {
  background-color: #00CCFF;
  clip-path: path('M 100,0 C 100,33.33 100,66.67 100,100 C 100,110 90,120 80,120 C 70,120 60,110 60,100 C 60,90 70,80 80,80 C 83.33,80 86.67,81 90,83 L 90,50 C 73.33,50 56.67,66.67 56.67,83.33 C 56.67,100 73.33,116.67 90,116.67 C 106.67,116.67 123.33,100 123.33,83.33 L 123.33,33.33 C 130,40 140,43.33 150,43.33 L 150,20 C 136.67,20 126.67,13.33 120,3.33 L 100,0 Z');
  animation: wave2 3s infinite ease-in-out 0.5s;
}

@keyframes wave1 {
  0% { transform: translateX(-5px); }
  50% { transform: translateX(5px); }
  100% { transform: translateX(-5px); }
}

@keyframes wave2 {
  0% { transform: translateX(5px); }
  50% { transform: translateX(-5px); }
  100% { transform: translateX(5px); }
}

.play-button {
  position: absolute;
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 3;
}

.play-icon {
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid #6C13B3;
  margin-left: 3px; /* Offset to center the triangle */
}

.app-name {
  color: white;
  font-size: 2.5rem;
  font-weight: bold;
  margin-top: 20px;
  font-family: 'Arial', sans-serif;
  letter-spacing: 1px;
}

.app-tagline {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  margin-top: 10px;
  font-style: italic;
}
