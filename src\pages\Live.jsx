import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiSearch } from 'react-icons/fi';
import TrendingHashtags from '../components/TrendingHashtags';
import SearchBar from '../components/SearchBar';
import GoLive from '../components/GoLive';
import LiveStreamView from '../components/LiveStreamView';
import './Live.css';

const Live = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [isLiveStreamVisible, setIsLiveStreamVisible] = useState(false);
  const [activeStream, setActiveStream] = useState(null);
  const [sortBy, setSortBy] = useState('trending');
  const [showSearch, setShowSearch] = useState(false);
  const [showGoLive, setShowGoLive] = useState(false);

  // Mock categories
  const categories = [
    { id: 'all', name: 'All' },
    { id: 'music', name: 'Music' },
    { id: 'gaming', name: 'Gaming' },
    { id: 'cooking', name: 'Cooking' },
    { id: 'talk', name: 'Talk Shows' },
    { id: 'dance', name: 'Dance' }
  ];

  // Mock live streams
  const liveStreams = [
    {
      id: 1,
      title: 'Live Music Session 🎵',
      username: 'alex_johnson',
      name: 'Alex Johnson',
      userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      thumbnail: 'https://placehold.co/600x400/FF3366/FFFFFF?text=Live+Music+Session',
      viewers: 1245,
      category: 'music',
      isVerified: true,
      isFeatured: true,
      tags: ['music', 'live', 'guitar']
    },
    {
      id: 2,
      title: 'Cooking Italian Pasta 🍝',
      username: 'sophia_garcia',
      name: 'Sophia Garcia',
      userAvatar: 'https://randomuser.me/api/portraits/women/56.jpg',
      thumbnail: 'https://placehold.co/600x400/00CCFF/FFFFFF?text=Cooking+Italian+Pasta',
      viewers: 876,
      category: 'cooking',
      isVerified: true,
      isFeatured: false,
      tags: ['cooking', 'italian', 'pasta']
    },
    {
      id: 3,
      title: 'Late Night Gaming 🎮',
      username: 'michael_brown',
      name: 'Michael Brown',
      userAvatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      thumbnail: 'https://placehold.co/600x400/6C13B3/FFFFFF?text=Late+Night+Gaming',
      viewers: 2345,
      category: 'gaming',
      isVerified: false,
      isFeatured: true,
      tags: ['gaming', 'fortnite', 'live']
    },
    {
      id: 4,
      title: 'Dance Practice Session 💃',
      username: 'emma_wilson',
      name: 'Emma Wilson',
      userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      thumbnail: 'https://placehold.co/600x400/FF9900/FFFFFF?text=Dance+Practice',
      viewers: 567,
      category: 'dance',
      isVerified: true,
      isFeatured: false,
      tags: ['dance', 'practice', 'choreography']
    },
    {
      id: 5,
      title: 'Q&A with Fans 🎤',
      username: 'daniel_lee',
      name: 'Daniel Lee',
      userAvatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      thumbnail: 'https://placehold.co/600x400/33CC33/FFFFFF?text=Q%26A+with+Fans',
      viewers: 1890,
      category: 'talk',
      isVerified: false,
      isFeatured: true,
      tags: ['talk', 'qa', 'fans']
    },
    {
      id: 6,
      title: 'Acoustic Guitar Session 🎸',
      username: 'alex_johnson',
      name: 'Alex Johnson',
      userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      thumbnail: 'https://placehold.co/600x400/3366FF/FFFFFF?text=Acoustic+Guitar',
      viewers: 765,
      category: 'music',
      isVerified: true,
      isFeatured: false,
      tags: ['music', 'acoustic', 'guitar']
    },
    {
      id: 7,
      title: 'Baking Birthday Cake 🎂',
      username: 'sophia_garcia',
      name: 'Sophia Garcia',
      userAvatar: 'https://randomuser.me/api/portraits/women/56.jpg',
      thumbnail: 'https://placehold.co/600x400/FF6600/FFFFFF?text=Baking+Birthday+Cake',
      viewers: 432,
      category: 'cooking',
      isVerified: true,
      isFeatured: false,
      tags: ['baking', 'cake', 'dessert']
    },
    {
      id: 8,
      title: 'Morning Talk Show ☕',
      username: 'daniel_lee',
      name: 'Daniel Lee',
      userAvatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      thumbnail: 'https://placehold.co/600x400/9933CC/FFFFFF?text=Morning+Talk+Show',
      viewers: 987,
      category: 'talk',
      isVerified: false,
      isFeatured: false,
      tags: ['talk', 'morning', 'show']
    }
  ];

  // Filter streams by category
  const getFilteredStreams = () => {
    if (activeCategory === 'all') {
      return liveStreams;
    }
    return liveStreams.filter(stream => stream.category === activeCategory);
  };

  // Get featured streams
  const getFeaturedStreams = () => {
    return liveStreams.filter(stream => stream.isFeatured);
  };

  // Handle stream click
  const handleStreamClick = (stream) => {
    setActiveStream(stream);
    setIsLiveStreamVisible(true);
  };

  return (
    <div className="live-container">
      <div className="live-header">
        <div className="header-left">
          <h1>Live</h1>
        </div>
        <div className="header-right">
          <div className="search-wrapper">
            {showSearch ? (
              <SearchBar expanded={true} onClose={() => setShowSearch(false)} />
            ) : (
              <button className="search-toggle-btn" onClick={() => setShowSearch(true)}>
                <FiSearch />
              </button>
            )}
          </div>
          <button
            className="go-live-btn btn btn-primary btn-lg"
            onClick={() => setShowGoLive(true)}
          >
            <span>Go Live</span>
          </button>
        </div>
      </div>

      <div className="live-content-wrapper">
        <div className="live-main-content">
          {getFeaturedStreams().length > 0 && (
            <div className="featured-streams">
              <h2>Featured Streams</h2>
              <div className="featured-grid">
                {getFeaturedStreams().map(stream => (
                  <div key={stream.id} className="featured-stream-card" onClick={() => handleStreamClick(stream)}>
                    <div className="stream-thumbnail">
                      <img src={stream.thumbnail} alt={stream.title} />
                      <div className="live-badge">LIVE</div>
                      <div className="stream-viewers">
                        <FiEye />
                        <span>{stream.viewers.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="stream-info">
                      <div className="stream-creator">
                        <img src={stream.userAvatar} alt={stream.name} />
                        <div>
                          <h3>
                            {stream.name}
                            {stream.isVerified && <span className="verified-badge">✓</span>}
                          </h3>
                          <span className="stream-category">{stream.category}</span>
                        </div>
                      </div>
                      <h4 className="stream-title">{stream.title}</h4>
                      <div className="stream-tags">
                        {stream.tags.map(tag => (
                          <span key={tag} className="stream-tag">#{tag}</span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="live-categories">
            <div className="categories-header">
              <h2>Browse Streams</h2>
              <div className="filter-dropdown">
                <button className="filter-btn btn btn-secondary btn-sm">
                  <FiFilter />
                  <span>Filter</span>
                </button>
              </div>
            </div>
            <div className="category-tabs">
              {categories.map(category => (
                <button
                  key={category.id}
                  className={`category-tab btn ${activeCategory === category.id ? 'btn-primary' : 'btn-secondary'}`}
                  onClick={() => setActiveCategory(category.id)}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>

          <div className="live-streams-grid">
            {getFilteredStreams().map(stream => (
              <div key={stream.id} className="stream-card" onClick={() => handleStreamClick(stream)}>
                <div className="stream-thumbnail">
                  <img src={stream.thumbnail} alt={stream.title} />
                  <div className="live-badge">LIVE</div>
                  <div className="stream-viewers">
                    <FiEye />
                    <span>{stream.viewers.toLocaleString()}</span>
                  </div>
                </div>
                <div className="stream-info">
                  <div className="stream-creator">
                    <img src={stream.userAvatar} alt={stream.name} />
                    <h3>
                      {stream.name}
                      {stream.isVerified && <span className="verified-badge">✓</span>}
                    </h3>
                  </div>
                  <h4 className="stream-title">{stream.title}</h4>
                  <div className="stream-category-tag">{stream.category}</div>
                </div>
              </div>
            ))}
          </div>
      </div>

      {/* Live Stream View - Shown when a stream is clicked */}
      {isLiveStreamVisible && (
        <LiveStreamView
          stream={activeStream}
          onClose={() => setIsLiveStreamVisible(false)}
        />
      )}

      {showGoLive && <GoLive onClose={() => setShowGoLive(false)} />}
    </div>
  );
};

export default Live;
