import React, { useState, useContext } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ock, FiBell, FiEye, FiGlobe, FiMoon, FiHelpCircle, FiShield, FiCreditCard, FiVideo, FiDownload, FiUpload, FiWifi, FiDatabase, FiSmartphone, FiTrash2, FiLogOut } from 'react-icons/fi';
import './Settings.css';

const Settings = () => {
  const [activeTab, setActiveTab] = useState('account');
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState('english');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [privateAccount, setPrivateAccount] = useState(false);
  const [autoplay, setAutoplay] = useState(true);
  const [dataSaver, setDataSaver] = useState(false);
  const [downloadOverWifi, setDownloadOverWifi] = useState(true);
  const [highQualityUploads, setHighQualityUploads] = useState(true);
  const [twoFactorAuth, setTwoFactorAuth] = useState(false);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  const handleDarkModeToggle = () => {
    setDarkMode(!darkMode);
    // In a real app, this would apply the theme globally
  };

  const handleLanguageChange = (e) => {
    setLanguage(e.target.value);
  };

  return (
    <div className="settings-container">
      <div className="settings-sidebar">
        <h2>Settings</h2>
        <ul className="settings-tabs">
          <li
            className={`settings-tab ${activeTab === 'account' ? 'active' : ''}`}
            onClick={() => handleTabChange('account')}
          >
            <FiUser />
            <span>Account</span>
          </li>
          <li
            className={`settings-tab ${activeTab === 'privacy' ? 'active' : ''}`}
            onClick={() => handleTabChange('privacy')}
          >
            <FiLock />
            <span>Privacy</span>
          </li>
          <li
            className={`settings-tab ${activeTab === 'security' ? 'active' : ''}`}
            onClick={() => handleTabChange('security')}
          >
            <FiShield />
            <span>Security</span>
          </li>
          <li
            className={`settings-tab ${activeTab === 'notifications' ? 'active' : ''}`}
            onClick={() => handleTabChange('notifications')}
          >
            <FiBell />
            <span>Notifications</span>
          </li>
          <li
            className={`settings-tab ${activeTab === 'appearance' ? 'active' : ''}`}
            onClick={() => handleTabChange('appearance')}
          >
            <FiEye />
            <span>Appearance</span>
          </li>
          <li
            className={`settings-tab ${activeTab === 'language' ? 'active' : ''}`}
            onClick={() => handleTabChange('language')}
          >
            <FiGlobe />
            <span>Language</span>
          </li>
          <li
            className={`settings-tab ${activeTab === 'data' ? 'active' : ''}`}
            onClick={() => handleTabChange('data')}
          >
            <FiDatabase />
            <span>Data & Storage</span>
          </li>
          <li
            className={`settings-tab ${activeTab === 'payments' ? 'active' : ''}`}
            onClick={() => handleTabChange('payments')}
          >
            <FiCreditCard />
            <span>Payments</span>
          </li>
          <li
            className={`settings-tab ${activeTab === 'content' ? 'active' : ''}`}
            onClick={() => handleTabChange('content')}
          >
            <FiVideo />
            <span>Content Preferences</span>
          </li>
          <li
            className={`settings-tab ${activeTab === 'help' ? 'active' : ''}`}
            onClick={() => handleTabChange('help')}
          >
            <FiHelpCircle />
            <span>Help</span>
          </li>
        </ul>
      </div>

      <div className="settings-content">
        {activeTab === 'account' && (
          <div className="settings-section">
            <h2>Account Settings</h2>
            <div className="settings-form">
              <div className="form-group">
                <label>Username</label>
                <input type="text" defaultValue="user123" />
              </div>
              <div className="form-group">
                <label>Email</label>
                <input type="email" defaultValue="<EMAIL>" />
              </div>
              <div className="form-group">
                <label>Phone</label>
                <input type="tel" defaultValue="+****************" />
              </div>
              <div className="form-group">
                <label>Bio</label>
                <textarea defaultValue="This is my VibeVid profile!" />
              </div>
              <button className="save-btn">Save Changes</button>
            </div>
          </div>
        )}

        {activeTab === 'privacy' && (
          <div className="settings-section">
            <h2>Privacy Settings</h2>
            <div className="settings-form">
              <div className="form-group switch-group">
                <div>
                  <label>Private Account</label>
                  <p className="setting-description">When your account is private, only users you approve can follow you and watch your videos.</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={privateAccount}
                    onChange={() => setPrivateAccount(!privateAccount)}
                  />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group">
                <label>Who can send you messages</label>
                <select defaultValue="followers">
                  <option value="everyone">Everyone</option>
                  <option value="followers">Followers</option>
                  <option value="following">People you follow</option>
                  <option value="none">No one</option>
                </select>
              </div>

              <div className="form-group">
                <label>Who can duet your videos</label>
                <select defaultValue="everyone">
                  <option value="everyone">Everyone</option>
                  <option value="followers">Followers</option>
                  <option value="following">People you follow</option>
                  <option value="none">No one</option>
                </select>
              </div>

              <div className="form-group">
                <label>Who can comment on your videos</label>
                <select defaultValue="everyone">
                  <option value="everyone">Everyone</option>
                  <option value="followers">Followers</option>
                  <option value="following">People you follow</option>
                  <option value="none">No one</option>
                </select>
              </div>

              <div className="form-group switch-group">
                <div>
                  <label>Download your data</label>
                  <p className="setting-description">Get a copy of your VibeVid data.</p>
                </div>
                <button className="secondary-btn">Request Data</button>
              </div>

              <button className="save-btn">Save Changes</button>
            </div>
          </div>
        )}

        {activeTab === 'notifications' && (
          <div className="settings-section">
            <h2>Notification Settings</h2>
            <div className="settings-form">
              <div className="form-group switch-group">
                <div>
                  <label>Push Notifications</label>
                  <p className="setting-description">Receive notifications even when you're not using the app.</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={notificationsEnabled}
                    onChange={() => setNotificationsEnabled(!notificationsEnabled)}
                  />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group switch-group">
                <div>
                  <label>Likes</label>
                  <p className="setting-description">Notify when someone likes your video.</p>
                </div>
                <label className="switch">
                  <input type="checkbox" defaultChecked />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group switch-group">
                <div>
                  <label>Comments</label>
                  <p className="setting-description">Notify when someone comments on your video.</p>
                </div>
                <label className="switch">
                  <input type="checkbox" defaultChecked />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group switch-group">
                <div>
                  <label>New Followers</label>
                  <p className="setting-description">Notify when someone follows you.</p>
                </div>
                <label className="switch">
                  <input type="checkbox" defaultChecked />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group switch-group">
                <div>
                  <label>Direct Messages</label>
                  <p className="setting-description">Notify when you receive a message.</p>
                </div>
                <label className="switch">
                  <input type="checkbox" defaultChecked />
                  <span className="slider round"></span>
                </label>
              </div>

              <button className="save-btn">Save Changes</button>
            </div>
          </div>
        )}

        {activeTab === 'appearance' && (
          <div className="settings-section">
            <h2>Appearance Settings</h2>
            <div className="settings-form">
              <div className="form-group switch-group">
                <div>
                  <label>Dark Mode</label>
                  <p className="setting-description">Switch between light and dark themes.</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={darkMode}
                    onChange={handleDarkModeToggle}
                  />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group switch-group">
                <div>
                  <label>Autoplay Videos</label>
                  <p className="setting-description">Videos will play automatically when scrolling.</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={autoplay}
                    onChange={() => setAutoplay(!autoplay)}
                  />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group">
                <label>Font Size</label>
                <select defaultValue="medium">
                  <option value="small">Small</option>
                  <option value="medium">Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>

              <button className="save-btn">Save Changes</button>
            </div>
          </div>
        )}

        {activeTab === 'language' && (
          <div className="settings-section">
            <h2>Language Settings</h2>
            <div className="settings-form">
              <div className="form-group">
                <label>App Language</label>
                <select
                  value={language}
                  onChange={handleLanguageChange}
                >
                  <option value="english">English</option>
                  <option value="spanish">Español</option>
                  <option value="french">Français</option>
                  <option value="german">Deutsch</option>
                  <option value="chinese">中文</option>
                  <option value="japanese">日本語</option>
                  <option value="korean">한국어</option>
                </select>
              </div>

              <div className="form-group">
                <label>Content Preferences</label>
                <select defaultValue="all">
                  <option value="all">All Languages</option>
                  <option value="app">Same as app language</option>
                </select>
              </div>

              <button className="save-btn">Save Changes</button>
            </div>
          </div>
        )}

        {activeTab === 'security' && (
          <div className="settings-section">
            <h2>Security Settings</h2>
            <div className="settings-form">
              <div className="form-group switch-group">
                <div>
                  <label>Two-Factor Authentication</label>
                  <p className="setting-description">Add an extra layer of security to your account.</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={twoFactorAuth}
                    onChange={() => setTwoFactorAuth(!twoFactorAuth)}
                  />
                  <span className="slider round"></span>
                </label>
              </div>

              {twoFactorAuth && (
                <div className="form-group">
                  <label>Verification Method</label>
                  <select defaultValue="sms">
                    <option value="sms">SMS</option>
                    <option value="email">Email</option>
                    <option value="app">Authentication App</option>
                  </select>
                </div>
              )}

              <div className="form-group">
                <label>Password</label>
                <div className="password-field">
                  <input type="password" defaultValue="********" />
                  <button className="secondary-btn">Change Password</button>
                </div>
              </div>

              <div className="form-group">
                <label>Login Activity</label>
                <div className="login-activity">
                  <div className="activity-item">
                    <div>
                      <FiSmartphone />
                      <div>
                        <p className="device-name">iPhone 13 - Los Angeles, CA</p>
                        <p className="activity-time">Active now</p>
                      </div>
                    </div>
                    <span className="current-device">Current Device</span>
                  </div>

                  <div className="activity-item">
                    <div>
                      <FiSmartphone />
                      <div>
                        <p className="device-name">MacBook Pro - Los Angeles, CA</p>
                        <p className="activity-time">2 days ago</p>
                      </div>
                    </div>
                    <button className="logout-btn">Log Out</button>
                  </div>
                </div>
              </div>

              <div className="form-group">
                <button className="danger-btn">
                  <FiLogOut /> Log Out of All Devices
                </button>
              </div>

              <button className="save-btn">Save Changes</button>
            </div>
          </div>
        )}

        {activeTab === 'data' && (
          <div className="settings-section">
            <h2>Data & Storage Settings</h2>
            <div className="settings-form">
              <div className="form-group switch-group">
                <div>
                  <label>Data Saver</label>
                  <p className="setting-description">Reduce data usage by loading videos at a lower quality.</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={dataSaver}
                    onChange={() => setDataSaver(!dataSaver)}
                  />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group switch-group">
                <div>
                  <label>Download Over Wi-Fi Only</label>
                  <p className="setting-description">Only download videos when connected to Wi-Fi.</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={downloadOverWifi}
                    onChange={() => setDownloadOverWifi(!downloadOverWifi)}
                  />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group switch-group">
                <div>
                  <label>High Quality Upload</label>
                  <p className="setting-description">Upload videos in high quality (uses more data).</p>
                </div>
                <label className="switch">
                  <input
                    type="checkbox"
                    checked={highQualityUploads}
                    onChange={() => setHighQualityUploads(!highQualityUploads)}
                  />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group">
                <label>Video Quality</label>
                <select defaultValue="auto">
                  <option value="auto">Auto</option>
                  <option value="low">Low (480p)</option>
                  <option value="medium">Medium (720p)</option>
                  <option value="high">High (1080p)</option>
                </select>
              </div>

              <div className="storage-usage">
                <h3>Storage Usage</h3>
                <div className="storage-bar">
                  <div className="storage-fill" style={{ width: '35%' }}></div>
                </div>
                <div className="storage-details">
                  <p>3.5 GB of 10 GB used</p>
                  <button className="secondary-btn">Clear Cache</button>
                </div>
              </div>

              <button className="save-btn">Save Changes</button>
            </div>
          </div>
        )}

        {activeTab === 'payments' && (
          <div className="settings-section">
            <h2>Payment Settings</h2>
            <div className="settings-form">
              <div className="payment-methods">
                <h3>Payment Methods</h3>
                <div className="payment-method-item">
                  <div className="payment-method-info">
                    <div className="payment-icon visa"></div>
                    <div>
                      <p className="payment-name">Visa ending in 4242</p>
                      <p className="payment-expiry">Expires 12/25</p>
                    </div>
                  </div>
                  <div className="payment-actions">
                    <button className="text-btn">Edit</button>
                    <button className="text-btn">Remove</button>
                  </div>
                </div>

                <button className="secondary-btn add-payment">
                  <FiCreditCard /> Add Payment Method
                </button>
              </div>

              <div className="billing-history">
                <h3>Billing History</h3>
                <div className="billing-item">
                  <div>
                    <p className="billing-date">May 15, 2023</p>
                    <p className="billing-description">VibeVid Pro Subscription</p>
                  </div>
                  <p className="billing-amount">$9.99</p>
                </div>

                <div className="billing-item">
                  <div>
                    <p className="billing-date">April 15, 2023</p>
                    <p className="billing-description">VibeVid Pro Subscription</p>
                  </div>
                  <p className="billing-amount">$9.99</p>
                </div>

                <div className="billing-item">
                  <div>
                    <p className="billing-date">March 15, 2023</p>
                    <p className="billing-description">VibeVid Pro Subscription</p>
                  </div>
                  <p className="billing-amount">$9.99</p>
                </div>

                <button className="text-btn view-all">View All Transactions</button>
              </div>

              <div className="subscription">
                <h3>Subscription</h3>
                <div className="subscription-info">
                  <div>
                    <p className="subscription-name">VibeVid Pro</p>
                    <p className="subscription-price">$9.99/month</p>
                  </div>
                  <button className="secondary-btn">Manage</button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'content' && (
          <div className="settings-section">
            <h2>Content Preferences</h2>
            <div className="settings-form">
              <div className="form-group">
                <label>Content Categories</label>
                <p className="setting-description">Select categories you're interested in to personalize your feed.</p>
                <div className="category-grid">
                  <div className="category-item selected">
                    <span>Comedy</span>
                  </div>
                  <div className="category-item selected">
                    <span>Music</span>
                  </div>
                  <div className="category-item">
                    <span>Dance</span>
                  </div>
                  <div className="category-item selected">
                    <span>Food</span>
                  </div>
                  <div className="category-item">
                    <span>Travel</span>
                  </div>
                  <div className="category-item selected">
                    <span>Sports</span>
                  </div>
                  <div className="category-item">
                    <span>Beauty</span>
                  </div>
                  <div className="category-item">
                    <span>Fashion</span>
                  </div>
                  <div className="category-item">
                    <span>DIY</span>
                  </div>
                  <div className="category-item selected">
                    <span>Gaming</span>
                  </div>
                  <div className="category-item">
                    <span>Education</span>
                  </div>
                  <div className="category-item">
                    <span>Pets</span>
                  </div>
                </div>
              </div>

              <div className="form-group">
                <label>Content Language</label>
                <select defaultValue="all">
                  <option value="all">All Languages</option>
                  <option value="english">English Only</option>
                  <option value="app">Same as App Language</option>
                </select>
              </div>

              <div className="form-group switch-group">
                <div>
                  <label>Restricted Mode</label>
                  <p className="setting-description">Filter out content that may not be appropriate for all audiences.</p>
                </div>
                <label className="switch">
                  <input type="checkbox" />
                  <span className="slider round"></span>
                </label>
              </div>

              <div className="form-group">
                <label>Not Interested</label>
                <p className="setting-description">Topics and hashtags you've marked as not interested.</p>
                <div className="not-interested-tags">
                  <div className="tag">
                    #challenge <button className="remove-tag">×</button>
                  </div>
                  <div className="tag">
                    #prank <button className="remove-tag">×</button>
                  </div>
                  <div className="tag">
                    #asmr <button className="remove-tag">×</button>
                  </div>
                </div>
              </div>

              <button className="save-btn">Save Changes</button>
            </div>
          </div>
        )}

        {activeTab === 'help' && (
          <div className="settings-section">
            <h2>Help & Support</h2>
            <div className="help-section">
              <div className="help-card">
                <FiShield size={24} />
                <h3>Report a Problem</h3>
                <p>Let us know about any issues you're experiencing.</p>
                <button className="secondary-btn">Report</button>
              </div>

              <div className="help-card">
                <FiHelpCircle size={24} />
                <h3>Help Center</h3>
                <p>Find answers to common questions.</p>
                <button className="secondary-btn">Visit Help Center</button>
              </div>

              <div className="help-card">
                <FiGlobe size={24} />
                <h3>Community Guidelines</h3>
                <p>Learn about our community standards.</p>
                <button className="secondary-btn">View Guidelines</button>
              </div>
            </div>

            <div className="app-info">
              <h3>About VibeVid</h3>
              <p>Version 1.0.0</p>
              <p>© 2023 VibeVid</p>
              <div className="links">
                <a href="#">Terms of Service</a>
                <a href="#">Privacy Policy</a>
                <a href="#">Cookies Policy</a>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Settings;
