import React, { useState, useEffect, useRef } from 'react';
import { FiChevronLeft, FiChevronRight, FiArrowUp } from 'react-icons/fi';
import './ScrollNavigation.css';

export const ScrollableContainer = ({ children, className, id }) => {
  const containerRef = useRef(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);

  const checkArrows = () => {
    if (!containerRef.current) return;
    
    const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
    setShowLeftArrow(scrollLeft > 0);
    setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
  };

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', checkArrows);
      // Initial check
      checkArrows();
      
      // Check again after content might have loaded
      setTimeout(checkArrows, 500);
    }
    
    return () => {
      if (container) {
        container.removeEventListener('scroll', checkArrows);
      }
    };
  }, []);

  const scrollLeft = () => {
    if (!containerRef.current) return;
    containerRef.current.scrollBy({ left: -300, behavior: 'smooth' });
  };

  const scrollRight = () => {
    if (!containerRef.current) return;
    containerRef.current.scrollBy({ left: 300, behavior: 'smooth' });
  };

  return (
    <div className={`scrollable-container-wrapper ${className || ''}`} id={id}>
      {showLeftArrow && (
        <button className="scroll-arrow scroll-left" onClick={scrollLeft} aria-label="Scroll left">
          <FiChevronLeft />
        </button>
      )}
      
      <div className="scrollable-container" ref={containerRef}>
        {children}
      </div>
      
      {showRightArrow && (
        <button className="scroll-arrow scroll-right" onClick={scrollRight} aria-label="Scroll right">
          <FiChevronRight />
        </button>
      )}
    </div>
  );
};

export const BackToTopButton = () => {
  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    const checkScroll = () => {
      if (window.pageYOffset > 300) {
        setShowButton(true);
      } else {
        setShowButton(false);
      }
    };

    window.addEventListener('scroll', checkScroll);
    return () => window.removeEventListener('scroll', checkScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <button 
      className={`back-to-top-button ${showButton ? 'visible' : ''}`} 
      onClick={scrollToTop}
      aria-label="Back to top"
    >
      <FiArrowUp />
    </button>
  );
};

export const SectionNavigation = ({ sections }) => {
  const [activeSection, setActiveSection] = useState(null);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 100;
      
      for (let i = sections.length - 1; i >= 0; i--) {
        const section = document.getElementById(sections[i].id);
        if (section && section.offsetTop <= scrollPosition) {
          setActiveSection(sections[i].id);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Initial check
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, [sections]);

  const scrollToSection = (sectionId) => {
    const section = document.getElementById(sectionId);
    if (section) {
      window.scrollTo({
        top: section.offsetTop - 80,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="section-navigation">
      {sections.map(section => (
        <button
          key={section.id}
          className={`section-nav-item ${activeSection === section.id ? 'active' : ''}`}
          onClick={() => scrollToSection(section.id)}
          aria-label={`Navigate to ${section.label}`}
        >
          {section.icon}
          <span>{section.label}</span>
        </button>
      ))}
    </div>
  );
};

export default { ScrollableContainer, BackToTopButton, SectionNavigation };
