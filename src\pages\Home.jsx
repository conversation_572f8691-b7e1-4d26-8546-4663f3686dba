import { useState, useEffect, useRef } from 'react';
import { FiTrendingUp, FiUsers, FiCompass, FiMusic, FiHash } from 'react-icons/fi';
import VideoCard from '../components/VideoCard';
import Comments from '../components/Comments';
import AutoplayToggle from '../components/AutoplayToggle';
import Hashtags from '../components/Hashtags';
import PersonalizedRecommendations from '../components/PersonalizedRecommendations';
import './Home.css';

const Home = () => {
  const [activeTab, setActiveTab] = useState('for-you');
  const [showComments, setShowComments] = useState(false);
  const [activeVideo, setActiveVideo] = useState(null);
  const [isAutoplay, setIsAutoplay] = useState(true);
  const [activeCategory, setActiveCategory] = useState('foryou');
  const videoRefs = useRef({});

  // Mock trending hashtags
  const trendingHashtags = [
    { tag: 'vibevid', count: '2.5B' },
    { tag: 'dance', count: '1.8B' },
    { tag: 'music', count: '1.2B' },
    { tag: 'comedy', count: '950M' },
    { tag: 'food', count: '780M' },
    { tag: 'travel', count: '650M' },
    { tag: 'pets', count: '520M' },
    { tag: 'fitness', count: '480M' },
  ];

  // Mock suggested accounts
  const suggestedAccounts = [
    { username: 'alex_johnson', name: 'Alex Johnson', avatar: 'https://randomuser.me/api/portraits/men/32.jpg', isVerified: true },
    { username: 'emma_wilson', name: 'Emma Wilson', avatar: 'https://randomuser.me/api/portraits/women/44.jpg', isVerified: true },
    { username: 'michael_brown', name: 'Michael Brown', avatar: 'https://randomuser.me/api/portraits/men/22.jpg', isVerified: false },
    { username: 'sophia_garcia', name: 'Sophia Garcia', avatar: 'https://randomuser.me/api/portraits/women/56.jpg', isVerified: true },
    { username: 'daniel_lee', name: 'Daniel Lee', avatar: 'https://randomuser.me/api/portraits/men/45.jpg', isVerified: false },
  ];

  // Mock video data
  const [forYouVideos, setForYouVideos] = useState([
    {
      id: 1,
      username: 'alex_johnson',
      name: 'Alex Johnson',
      userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      isVerified: true,
      caption: 'Beach day vibes! Perfect weather for a swim 🏖️ #summer #beach',
      tags: ['summer', 'beach'],
      music: 'Summer Hits - Beach Tunes',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-young-woman-waving-her-hair-in-a-pool-1229-large.mp4',
      likes: 1234,
      comments: 123,
      shares: 45,
      saved: 67
    },
    {
      id: 2,
      username: 'emma_wilson',
      name: 'Emma Wilson',
      userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      isVerified: true,
      caption: 'New dance routine! Let me know what you think 💃 #dance #viral',
      tags: ['dance', 'viral'],
      music: 'Popular Hit - Famous Artist',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-man-dancing-under-changing-lights-1240-large.mp4',
      likes: 5678,
      comments: 456,
      shares: 78,
      saved: 123
    },
    {
      id: 3,
      username: 'michael_brown',
      name: 'Michael Brown',
      userAvatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      isVerified: false,
      caption: 'Nature walk today! The colors are amazing this time of year 🌲 #nature #outdoors',
      tags: ['nature', 'outdoors'],
      music: 'Chill Vibes - Relaxing Artist',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-tree-with-yellow-flowers-1173-large.mp4',
      likes: 9012,
      comments: 789,
      shares: 123,
      saved: 45
    },
    {
      id: 4,
      username: 'sophia_garcia',
      name: 'Sophia Garcia',
      userAvatar: 'https://randomuser.me/api/portraits/women/56.jpg',
      isVerified: true,
      caption: 'City lights and night vibes 🌃 #city #night #urban',
      tags: ['city', 'night', 'urban'],
      music: 'Urban Beats - City Sounds',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-lights-of-times-square-in-new-york-city-4264-large.mp4',
      likes: 15678,
      comments: 987,
      shares: 432,
      saved: 210
    },
    {
      id: 5,
      username: 'daniel_lee',
      name: 'Daniel Lee',
      userAvatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      isVerified: false,
      caption: 'Morning coffee and work setup ☕ #productivity #coffee #morning',
      tags: ['productivity', 'coffee', 'morning'],
      music: 'Morning Jazz - Cafe Vibes',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-top-view-of-a-table-with-a-laptop-and-a-30708-large.mp4',
      likes: 3456,
      comments: 234,
      shares: 56,
      saved: 78
    },
    {
      id: 6,
      username: 'jessica_taylor',
      name: 'Jessica Taylor',
      userAvatar: 'https://randomuser.me/api/portraits/women/67.jpg',
      isVerified: true,
      caption: 'Sunset hike with friends was amazing! 🌄 #hiking #adventure #sunset',
      tags: ['hiking', 'adventure', 'sunset'],
      music: 'Adventure Sounds - Nature Mix',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-silhouette-of-a-woman-walking-on-a-mountain-at-sunset-33417-large.mp4',
      likes: 7823,
      comments: 432,
      shares: 156,
      saved: 234
    },
    {
      id: 7,
      username: 'ryan_miller',
      name: 'Ryan Miller',
      userAvatar: 'https://randomuser.me/api/portraits/men/55.jpg',
      isVerified: false,
      caption: 'Just finished this painting! What do you think? 🎨 #art #painting #creative',
      tags: ['art', 'painting', 'creative'],
      music: 'Creative Flow - Artistic Beats',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-top-view-of-a-woman-drawing-a-mandala-43486-large.mp4',
      likes: 4567,
      comments: 345,
      shares: 87,
      saved: 123
    },
    {
      id: 8,
      username: 'olivia_martinez',
      name: 'Olivia Martinez',
      userAvatar: 'https://randomuser.me/api/portraits/women/23.jpg',
      isVerified: true,
      caption: 'Beach volleyball tournament today! 🏐 #volleyball #beach #sports',
      tags: ['volleyball', 'beach', 'sports'],
      music: 'Sports Anthem - Summer Edition',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-playing-beach-volleyball-in-the-summer-1783-large.mp4',
      likes: 8901,
      comments: 567,
      shares: 234,
      saved: 178
    },
    {
      id: 9,
      username: 'noah_wilson',
      name: 'Noah Wilson',
      userAvatar: 'https://randomuser.me/api/portraits/men/62.jpg',
      isVerified: false,
      caption: 'Road trip through the mountains! 🚗 #travel #roadtrip #mountains',
      tags: ['travel', 'roadtrip', 'mountains'],
      music: 'Road Trip Playlist - Driving Beats',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-road-in-the-middle-of-a-mountain-range-4633-large.mp4',
      likes: 6789,
      comments: 432,
      shares: 198,
      saved: 245
    },
    {
      id: 10,
      username: 'ava_thompson',
      name: 'Ava Thompson',
      userAvatar: 'https://randomuser.me/api/portraits/women/89.jpg',
      isVerified: true,
      caption: 'Making my favorite pasta recipe! 🍝 #cooking #food #recipe',
      tags: ['cooking', 'food', 'recipe'],
      music: 'Cooking Show - Food Network',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-cooking-with-a-pan-on-a-stove-2753-large.mp4',
      likes: 5432,
      comments: 321,
      shares: 87,
      saved: 156
    }
  ]);

  const [followingVideos, setFollowingVideos] = useState([
    {
      id: 6,
      username: 'alex_johnson',
      name: 'Alex Johnson',
      userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      isVerified: true,
      caption: 'Trying out this new recipe! So delicious 😋 #food #cooking',
      tags: ['food', 'cooking'],
      music: 'Cooking Beats - Food Channel',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-cooking-with-a-pan-on-a-stove-2753-large.mp4',
      likes: 2345,
      comments: 178,
      shares: 67,
      saved: 89
    },
    {
      id: 7,
      username: 'emma_wilson',
      name: 'Emma Wilson',
      userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      isVerified: true,
      caption: 'Workout of the day! Try this routine 💪 #fitness #workout',
      tags: ['fitness', 'workout'],
      music: 'Workout Mix - Fitness Beats',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-woman-doing-exercises-at-home-3775-large.mp4',
      likes: 7890,
      comments: 567,
      shares: 123,
      saved: 234
    },
    {
      id: 8,
      username: 'michael_brown',
      name: 'Michael Brown',
      userAvatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      isVerified: false,
      caption: 'My new puppy! Isn\'t he adorable? 🐶 #pets #puppy #cute',
      tags: ['pets', 'puppy', 'cute'],
      music: 'Happy Tunes - Pet Lovers',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-a-small-dog-is-played-with-a-stuffed-toy-4538-large.mp4',
      likes: 12345,
      comments: 876,
      shares: 345,
      saved: 123
    }
  ]);

  // Handle video visibility and autoplay
  useEffect(() => {
    const handleScroll = () => {
      if (!isAutoplay) return;

      const videos = activeTab === 'for-you' ? forYouVideos : followingVideos;

      videos.forEach(video => {
        const videoElement = videoRefs.current[video.id];
        if (!videoElement) return;

        const rect = videoElement.getBoundingClientRect();
        const isVisible =
          rect.top >= 0 &&
          rect.top <= window.innerHeight - (rect.height / 2);

        if (isVisible) {
          videoElement.play();
        } else {
          videoElement.pause();
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    // Initial check
    setTimeout(handleScroll, 1000);

    return () => window.removeEventListener('scroll', handleScroll);
  }, [activeTab, isAutoplay, forYouVideos, followingVideos]);

  const handleVideoRef = (id, element) => {
    videoRefs.current[id] = element;
  };

  const handleCommentClick = (video) => {
    setActiveVideo(video);
    setShowComments(true);
  };

  const getActiveVideos = () => {
    return activeTab === 'for-you' ? forYouVideos : followingVideos;
  };

  return (
    <div className="home-container">
      <div className="home-layout">
        <div className="home-sidebar">
          <div className="home-tabs">
            <button
              className={`home-tab ${activeTab === 'for-you' ? 'active' : ''}`}
              onClick={() => setActiveTab('for-you')}
            >
              <FiCompass />
              <span>For You</span>
            </button>
            <button
              className={`home-tab ${activeTab === 'following' ? 'active' : ''}`}
              onClick={() => setActiveTab('following')}
            >
              <FiUsers />
              <span>Following</span>
            </button>
          </div>

          <div className="sidebar-section">
            <h3 className="sidebar-title">
              <FiTrendingUp />
              <span>Trending Hashtags</span>
            </h3>
            <div className="trending-hashtags">
              {trendingHashtags.map(hashtag => (
                <div key={hashtag.tag} className="trending-hashtag">
                  <FiHash />
                  <div className="hashtag-info">
                    <span className="hashtag-name">{hashtag.tag}</span>
                    <span className="hashtag-count">{hashtag.count} views</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="sidebar-section">
            <h3 className="sidebar-title">
              <FiUsers />
              <span>Suggested Accounts</span>
            </h3>
            <div className="suggested-accounts">
              {suggestedAccounts.map(account => (
                <div key={account.username} className="suggested-account">
                  <img src={account.avatar} alt={account.name} className="account-avatar" />
                  <div className="account-info">
                    <div className="account-name">
                      <span>{account.name}</span>
                      {account.isVerified && <span className="verified-badge">✓</span>}
                    </div>
                    <span className="account-username">@{account.username}</span>
                  </div>
                  <button className="follow-btn btn btn-primary btn-sm">Follow</button>
                </div>
              ))}
            </div>
          </div>

          <div className="sidebar-section">
            <h3 className="sidebar-title">
              <FiMusic />
              <span>Popular Music</span>
            </h3>
            <div className="popular-music">
              {getActiveVideos().slice(0, 3).map(video => (
                <div key={`music-${video.id}`} className="music-item">
                  <div className="music-icon">
                    <FiMusic />
                  </div>
                  <div className="music-info">
                    <span className="music-name">{video.music}</span>
                    <span className="music-usage">{Math.floor(Math.random() * 10000)} videos</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="sidebar-footer">
            <p>© 2023 VibeVid</p>
            <div className="footer-links">
              <a href="#">About</a>
              <a href="#">Terms</a>
              <a href="#">Privacy</a>
            </div>
          </div>
        </div>

        <div className="video-feed">
          <div className="feed-header">
            <h2>{activeTab === 'for-you' ? 'For You' : 'Following'}</h2>
            <div className="feed-controls">
              <AutoplayToggle
                isEnabled={isAutoplay}
                onChange={setIsAutoplay}
                size="medium"
              />
            </div>
          </div>

          {activeTab === 'for-you' && (
            <PersonalizedRecommendations
              onCategorySelect={(category) => setActiveCategory(category)}
            />
          )}

          {getActiveVideos().map(video => (
            <VideoCard
              key={video.id}
              video={video}
              onVideoRef={(element) => handleVideoRef(video.id, element)}
              onCommentClick={() => handleCommentClick(video)}
              autoplay={isAutoplay}
            />
          ))}
        </div>
      </div>

      {showComments && activeVideo && (
        <Comments
          video={activeVideo}
          onClose={() => setShowComments(false)}
        />
      )}
    </div>
  );
};

export default Home;
