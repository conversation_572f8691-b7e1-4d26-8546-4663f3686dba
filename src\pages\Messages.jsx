import React, { useState } from 'react';
import { FiSend, FiMoreHorizontal, FiSearch, FiVideo, FiImage, FiSmile, FiMic } from 'react-icons/fi';
import './Messages.css';

const Messages = () => {
  const [activeChat, setActiveChat] = useState(1);
  const [messageText, setMessageText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Mock conversations data
  const conversations = [
    {
      id: 1,
      user: {
        username: 'alex_johnson',
        name: '<PERSON>',
        avatar: 'https://placehold.co/50',
        isOnline: true
      },
      lastMessage: {
        text: 'That video was amazing! How did you film it?',
        time: '10:30 AM',
        isRead: true,
        isSent: false
      },
      unreadCount: 0
    },
    {
      id: 2,
      user: {
        username: 'emma_wilson',
        name: '<PERSON>',
        avatar: 'https://placehold.co/50',
        isOnline: false
      },
      lastMessage: {
        text: 'Thanks for the follow! Love your content.',
        time: 'Yesterday',
        isRead: false,
        isSent: false
      },
      unreadCount: 1
    },
    {
      id: 3,
      user: {
        username: 'mi<PERSON><PERSON>_brown',
        name: '<PERSON>',
        avatar: 'https://placehold.co/50',
        isOnline: true
      },
      lastMessage: {
        text: 'Let me know when you want to collab!',
        time: 'Yesterday',
        isRead: true,
        isSent: true
      },
      unreadCount: 0
    },
    {
      id: 4,
      user: {
        username: 'sophia_garcia',
        name: 'Sophia Garcia',
        avatar: 'https://placehold.co/50',
        isOnline: false
      },
      lastMessage: {
        text: 'Did you see the new trending challenge?',
        time: 'Monday',
        isRead: true,
        isSent: false
      },
      unreadCount: 0
    },
    {
      id: 5,
      user: {
        username: 'daniel_lee',
        name: 'Daniel Lee',
        avatar: 'https://placehold.co/50',
        isOnline: false
      },
      lastMessage: {
        text: 'Your latest video is fire! 🔥',
        time: 'Sunday',
        isRead: true,
        isSent: false
      },
      unreadCount: 0
    }
  ];
  
  // Mock messages for the active conversation
  const messages = [
    {
      id: 1,
      text: 'Hey there! I really enjoyed your latest video.',
      time: '10:15 AM',
      isSent: false,
      isRead: true
    },
    {
      id: 2,
      text: 'Thanks! I spent a lot of time editing it.',
      time: '10:17 AM',
      isSent: true,
      isRead: true
    },
    {
      id: 3,
      text: 'What software do you use for editing?',
      time: '10:20 AM',
      isSent: false,
      isRead: true
    },
    {
      id: 4,
      text: 'I use Adobe Premiere Pro for most of my videos. Sometimes After Effects for special effects.',
      time: '10:22 AM',
      isSent: true,
      isRead: true
    },
    {
      id: 5,
      text: 'That video was amazing! How did you film it?',
      time: '10:30 AM',
      isSent: false,
      isRead: true
    }
  ];
  
  const getFilteredConversations = () => {
    if (!searchQuery) return conversations;
    
    return conversations.filter(conversation => 
      conversation.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conversation.user.username.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };
  
  const handleSendMessage = (e) => {
    e.preventDefault();
    if (!messageText.trim()) return;
    
    // In a real app, you would send the message to the server
    console.log('Sending message:', messageText);
    setMessageText('');
  };
  
  const getActiveConversation = () => {
    return conversations.find(conversation => conversation.id === activeChat);
  };
  
  return (
    <div className="messages-container">
      <div className="messages-sidebar">
        <div className="messages-header">
          <h2>Messages</h2>
          <button className="new-message-btn">New Message</button>
        </div>
        
        <div className="search-messages">
          <FiSearch className="search-icon" />
          <input 
            type="text" 
            placeholder="Search messages" 
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="conversations-list">
          {getFilteredConversations().length > 0 ? (
            getFilteredConversations().map(conversation => (
              <div 
                key={conversation.id} 
                className={`conversation-item ${activeChat === conversation.id ? 'active' : ''} ${conversation.unreadCount > 0 ? 'unread' : ''}`}
                onClick={() => setActiveChat(conversation.id)}
              >
                <div className="conversation-avatar">
                  <img src={conversation.user.avatar} alt={conversation.user.name} />
                  {conversation.user.isOnline && <span className="online-indicator"></span>}
                </div>
                
                <div className="conversation-info">
                  <div className="conversation-header">
                    <h3 className="conversation-name">{conversation.user.name}</h3>
                    <span className="conversation-time">{conversation.lastMessage.time}</span>
                  </div>
                  
                  <div className="conversation-preview">
                    {conversation.lastMessage.isSent && <span className="sent-indicator">You: </span>}
                    <p className="preview-text">{conversation.lastMessage.text}</p>
                    
                    {conversation.unreadCount > 0 && (
                      <span className="unread-badge">{conversation.unreadCount}</span>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="no-conversations">
              <p>No conversations found</p>
            </div>
          )}
        </div>
      </div>
      
      <div className="chat-container">
        {activeChat ? (
          <>
            <div className="chat-header">
              <div className="chat-user-info">
                <img 
                  src={getActiveConversation().user.avatar} 
                  alt={getActiveConversation().user.name} 
                  className="chat-avatar" 
                />
                <div>
                  <h3 className="chat-username">{getActiveConversation().user.name}</h3>
                  <span className="chat-status">
                    {getActiveConversation().user.isOnline ? 'Online' : 'Offline'}
                  </span>
                </div>
              </div>
              
              <div className="chat-actions">
                <button className="chat-action-btn">
                  <FiVideo />
                </button>
                <button className="chat-action-btn">
                  <FiMoreHorizontal />
                </button>
              </div>
            </div>
            
            <div className="chat-messages">
              {messages.map(message => (
                <div 
                  key={message.id} 
                  className={`message ${message.isSent ? 'sent' : 'received'}`}
                >
                  <div className="message-content">
                    <p>{message.text}</p>
                    <span className="message-time">{message.time}</span>
                  </div>
                </div>
              ))}
            </div>
            
            <form className="message-input-container" onSubmit={handleSendMessage}>
              <div className="message-attachments">
                <button type="button" className="attachment-btn">
                  <FiImage />
                </button>
                <button type="button" className="attachment-btn">
                  <FiMic />
                </button>
                <button type="button" className="attachment-btn">
                  <FiSmile />
                </button>
              </div>
              
              <input 
                type="text" 
                placeholder="Type a message..." 
                value={messageText}
                onChange={(e) => setMessageText(e.target.value)}
              />
              
              <button type="submit" className="send-btn" disabled={!messageText.trim()}>
                <FiSend />
              </button>
            </form>
          </>
        ) : (
          <div className="empty-chat">
            <div className="empty-chat-content">
              <h3>Select a conversation</h3>
              <p>Choose a conversation from the list or start a new one</p>
              <button className="new-message-btn">New Message</button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Messages;
