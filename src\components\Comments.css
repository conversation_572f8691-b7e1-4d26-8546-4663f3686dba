.comments-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.comments-container {
  width: 100%;
  max-width: 500px;
  height: 80vh;
  background-color: var(--white);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.dark .comments-container {
  background-color: #1e1e1e;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .comments-header {
  border-bottom-color: #2a2a2a;
}

.comments-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dark .close-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.close-btn:hover {
  background-color: var(--light-gray);
}

.dark .close-btn:hover {
  background-color: #3a3a3a;
}

.comments-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.comment {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.comment-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.comment-user {
  display: flex;
  align-items: center;
  gap: 5px;
}

.comment-username {
  font-weight: 600;
  font-size: 0.95rem;
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.8rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.comment-handle {
  font-size: 0.85rem;
  color: var(--dark-gray);
}

.dark .comment-handle {
  color: #aaa;
}

.comment-time {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .comment-time {
  color: #aaa;
}

.comment-text {
  font-size: 0.95rem;
  line-height: 1.4;
  margin-bottom: 8px;
}

.comment-actions {
  display: flex;
  gap: 15px;
}

.like-btn, .reply-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background: transparent;
  border: none;
  font-size: 0.85rem;
  color: var(--dark-gray);
  cursor: pointer;
  padding: 5px 0;
}

.dark .like-btn, 
.dark .reply-btn {
  color: #aaa;
}

.like-btn.liked {
  color: #FF3366;
}

.comment-replies {
  margin-top: 15px;
  margin-left: 10px;
  padding-left: 10px;
  border-left: 1px solid var(--light-gray);
}

.dark .comment-replies {
  border-left-color: #2a2a2a;
}

.reply {
  display: flex;
  gap: 12px;
  margin-bottom: 15px;
}

.reply:last-child {
  margin-bottom: 0;
}

.reply-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
}

.comment-input-container {
  padding: 15px 20px;
  border-top: 1px solid var(--light-gray);
}

.dark .comment-input-container {
  border-top-color: #2a2a2a;
}

.replying-to {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--gray);
  border-radius: 8px;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.dark .replying-to {
  background-color: #2a2a2a;
}

.cancel-reply {
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--dark-gray);
}

.dark .cancel-reply {
  color: #aaa;
}

.comment-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid var(--light-gray);
  border-radius: 24px;
  padding: 8px 15px;
  background-color: var(--white);
}

.dark .comment-input-wrapper {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
}

.comment-input-wrapper input {
  flex: 1;
  border: none;
  outline: none;
  padding: 8px 0;
  font-size: 0.95rem;
  background-color: transparent;
}

.dark .comment-input-wrapper input {
  color: var(--white);
}

.dark .comment-input-wrapper input::placeholder {
  color: #aaa;
}

.comment-input-actions {
  display: flex;
  gap: 10px;
}

.emoji-btn, .post-btn {
  background: transparent;
  border: none;
  cursor: pointer;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.emoji-btn {
  color: var(--dark-gray);
}

.dark .emoji-btn {
  color: #aaa;
}

.emoji-btn:hover {
  background-color: var(--gray);
}

.dark .emoji-btn:hover {
  background-color: #3a3a3a;
}

.post-btn {
  color: #6C13B3;
}

.dark .post-btn {
  color: #9B4BDE;
}

.post-btn:hover {
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .post-btn:hover {
  background-color: rgba(155, 75, 222, 0.2);
}

.post-btn:disabled {
  color: var(--dark-gray);
  cursor: not-allowed;
}

.dark .post-btn:disabled {
  color: #aaa;
}

.post-btn:disabled:hover {
  background-color: transparent;
}

/* For mobile devices */
@media (max-width: 768px) {
  .comments-container {
    width: 100%;
    height: 90vh;
    max-width: none;
    border-radius: 12px 12px 0 0;
    margin-top: auto;
    margin-bottom: 0;
  }
  
  .comments-list {
    padding: 15px;
  }
}
