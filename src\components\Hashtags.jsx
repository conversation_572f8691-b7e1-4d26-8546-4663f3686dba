import React from 'react';
import { Link } from 'react-router-dom';
import './Hashtags.css';

const Hashtags = ({ tags, size = 'medium', color = 'default' }) => {
  // Size variants: 'small', 'medium', 'large'
  // Color variants: 'default', 'primary', 'gradient'
  
  return (
    <div className={`hashtags-container size-${size}`}>
      {tags.map((tag, index) => (
        <Link 
          key={index} 
          to={`/explore/tag/${tag}`} 
          className={`hashtag-link color-${color}`}
        >
          #{tag}
        </Link>
      ))}
    </div>
  );
};

export default Hashtags;
