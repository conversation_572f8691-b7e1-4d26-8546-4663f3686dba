* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
}

:root {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --primary-color: #6C13B3;
  --secondary-color: #00CCFF;
  --accent-color: #FF3366;
  --text-color: #161823;
  --white: #ffffff;
  --gray: #f1f1f2;
  --light-gray: #e3e3e4;
  --dark-gray: #8a8a8a;
  --black: #000000;
  --success-color: #33CC33;
  --error-color: #FF3366;
  --warning-color: #FF9900;
}

body {
  margin: 0;
  font-family: inherit;
  background-color: var(--white);
  color: var(--text-color);
  overflow-x: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
}

body.dark-theme {
  background-color: #121212;
  color: var(--white);
}

a {
  text-decoration: none;
  color: inherit;
}

input, textarea {
  user-select: text;
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

button::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.3s, opacity 0.5s;
}

button:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(108, 19, 179, 0.2);
}

.dark button:focus {
  box-shadow: 0 0 0 2px rgba(155, 75, 222, 0.3);
}

ul, ol {
  list-style: none;
}

.app-container {
  max-width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Enhanced Button Styles */
.btn {
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-2px);
}

.btn:active {
  transform: translateY(1px);
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: #5a0e9c;
  box-shadow: 0 4px 12px rgba(108, 19, 179, 0.2);
}

.dark .btn-primary:hover {
  background-color: #8a3dcf;
  box-shadow: 0 4px 12px rgba(155, 75, 222, 0.3);
}

.btn-secondary {
  background-color: var(--gray);
  color: var(--text-color);
}

.dark .btn-secondary {
  background-color: #2a2a2a;
  color: var(--white);
}

.btn-secondary:hover {
  background-color: var(--light-gray);
}

.dark .btn-secondary:hover {
  background-color: #3a3a3a;
}

.btn-outline {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background-color: transparent;
}

.dark .btn-outline {
  border-color: #9B4BDE;
  color: #9B4BDE;
}

.btn-outline:hover {
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .btn-outline:hover {
  background-color: rgba(155, 75, 222, 0.1);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.85rem;
  border-radius: 6px;
}

.btn-lg {
  padding: 12px 20px;
  font-size: 1.1rem;
  border-radius: 10px;
}

.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon.btn-sm {
  width: 32px;
  height: 32px;
}

.btn-icon.btn-lg {
  width: 48px;
  height: 48px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .btn {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .btn-sm {
    padding: 6px 10px;
    font-size: 0.8rem;
  }

  .btn-lg {
    padding: 10px 16px;
    font-size: 1rem;
  }

  .btn-icon {
    width: 36px;
    height: 36px;
  }

  .btn-icon.btn-sm {
    width: 28px;
    height: 28px;
  }

  .btn-icon.btn-lg {
    width: 44px;
    height: 44px;
  }
}

/* Touch improvements */
@media (hover: none) and (pointer: coarse) {
  .btn:hover {
    transform: none;
  }

  .btn:active {
    transform: scale(0.95);
  }
}
