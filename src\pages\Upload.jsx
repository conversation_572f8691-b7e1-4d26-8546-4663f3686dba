import { useState, useRef } from 'react';
import { FiUpload, FiX } from 'react-icons/fi';
import './Upload.css';

const Upload = () => {
  const [videoFile, setVideoFile] = useState(null);
  const [videoPreview, setVideoPreview] = useState(null);
  const [caption, setCaption] = useState('');
  const [tags, setTags] = useState('');
  const [music, setMusic] = useState('Original Sound');
  const [isPrivate, setIsPrivate] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef(null);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith('video/')) {
      setVideoFile(file);
      const videoUrl = URL.createObjectURL(file);
      setVideoPreview(videoUrl);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('video/')) {
      setVideoFile(file);
      const videoUrl = URL.createObjectURL(file);
      setVideoPreview(videoUrl);
    }
  };

  const handleUpload = (e) => {
    e.preventDefault();
    if (!videoFile) return;

    setIsUploading(true);
    
    // Mock upload process
    setTimeout(() => {
      setIsUploading(false);
      // Reset form
      setVideoFile(null);
      setVideoPreview(null);
      setCaption('');
      setTags('');
      setMusic('Original Sound');
      setIsPrivate(false);
      
      // Show success message (in a real app, you'd use a toast or notification)
      alert('Video uploaded successfully!');
    }, 2000);
  };

  const handleRemoveVideo = () => {
    setVideoFile(null);
    setVideoPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="upload-container">
      <h1>Upload video</h1>
      <p className="upload-subtitle">Post a video to your account</p>

      <div className="upload-content">
        <div 
          className="upload-video-container"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {!videoPreview ? (
            <div className="upload-placeholder">
              <FiUpload className="upload-icon" />
              <p>Select video to upload</p>
              <p className="upload-hint">Or drag and drop a file</p>
              <p className="upload-info">MP4 or WebM</p>
              <p className="upload-info">Up to 10 minutes</p>
              <p className="upload-info">Less than 2 GB</p>
              <label className="select-file-btn">
                Select file
                <input 
                  type="file" 
                  accept="video/*" 
                  onChange={handleFileChange}
                  ref={fileInputRef}
                  hidden
                />
              </label>
            </div>
          ) : (
            <div className="video-preview-container">
              <button className="remove-video-btn" onClick={handleRemoveVideo}>
                <FiX />
              </button>
              <video 
                src={videoPreview} 
                className="video-preview" 
                controls
              />
            </div>
          )}
        </div>

        <form className="upload-form" onSubmit={handleUpload}>
          <div className="form-group">
            <label htmlFor="caption">Caption</label>
            <textarea
              id="caption"
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              placeholder="Write a caption..."
              maxLength={150}
            />
            <div className="char-count">{caption.length}/150</div>
          </div>

          <div className="form-group">
            <label htmlFor="tags">Tags</label>
            <input
              type="text"
              id="tags"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              placeholder="Add tags separated by spaces (e.g. funny dance viral)"
            />
          </div>

          <div className="form-group">
            <label htmlFor="music">Music</label>
            <input
              type="text"
              id="music"
              value={music}
              onChange={(e) => setMusic(e.target.value)}
              placeholder="Add music title"
            />
          </div>

          <div className="form-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={isPrivate}
                onChange={(e) => setIsPrivate(e.target.checked)}
              />
              Private video
            </label>
          </div>

          <div className="form-actions">
            <button type="button" className="discard-btn">Discard</button>
            <button 
              type="submit" 
              className="post-btn"
              disabled={!videoFile || isUploading}
            >
              {isUploading ? 'Posting...' : 'Post'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Upload;
