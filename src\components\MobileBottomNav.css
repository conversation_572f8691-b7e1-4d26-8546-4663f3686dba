.mobile-bottom-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--white);
  border-top: 1px solid var(--light-gray);
  padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
  z-index: 100;
  box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.mobile-bottom-nav.dark {
  background-color: #121212;
  border-top-color: #2a2a2a;
  box-shadow: 0 -2px 15px rgba(0, 0, 0, 0.3);
}

.mobile-bottom-nav {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 70px;
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px 12px;
  text-decoration: none;
  color: var(--dark-gray);
  transition: all 0.3s ease;
  border-radius: 12px;
  min-width: 60px;
  position: relative;
}

.dark .bottom-nav-item {
  color: #aaa;
}

.bottom-nav-item:hover {
  color: var(--text-color);
  background-color: var(--gray);
}

.dark .bottom-nav-item:hover {
  color: var(--white);
  background-color: #2a2a2a;
}

.bottom-nav-item.active {
  color: #6C13B3;
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .bottom-nav-item.active {
  color: #9B4BDE;
  background-color: rgba(155, 75, 222, 0.2);
}

.bottom-nav-item.upload-item {
  background-color: #6C13B3;
  color: white;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  margin: 0 8px;
  box-shadow: 0 4px 15px rgba(108, 19, 179, 0.3);
}

.dark .bottom-nav-item.upload-item {
  background-color: #9B4BDE;
  box-shadow: 0 4px 15px rgba(155, 75, 222, 0.4);
}

.bottom-nav-item.upload-item:hover {
  background-color: #5a0e9c;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 19, 179, 0.4);
}

.dark .bottom-nav-item.upload-item:hover {
  background-color: #8a3dcf;
  box-shadow: 0 6px 20px rgba(155, 75, 222, 0.5);
}

.bottom-nav-item.upload-item.active {
  background-color: #6C13B3;
  color: white;
}

.dark .bottom-nav-item.upload-item.active {
  background-color: #9B4BDE;
  color: white;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.bottom-nav-item.active .nav-icon {
  transform: scale(1.1);
}

.bottom-nav-item.upload-item .nav-icon {
  transform: none;
}

.nav-label {
  font-size: 0.7rem;
  font-weight: 500;
  text-align: center;
  line-height: 1;
}

.bottom-nav-item.upload-item .nav-label {
  display: none;
}

/* Active indicator */
.bottom-nav-item.active::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background-color: #6C13B3;
  border-radius: 0 0 3px 3px;
}

.dark .bottom-nav-item.active::before {
  background-color: #9B4BDE;
}

.bottom-nav-item.upload-item::before {
  display: none;
}

/* Show only on mobile */
@media (max-width: 768px) {
  .mobile-bottom-nav {
    display: flex;
  }
  
  /* Add bottom padding to body to account for fixed bottom nav */
  body {
    padding-bottom: 70px;
  }
}

/* Hide on desktop */
@media (min-width: 769px) {
  .mobile-bottom-nav {
    display: none;
  }
}
