.following-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.following-header {
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.following-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
}

.following-filters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  align-items: center;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  /* Other styles are handled by global button classes */
}

.filter-dropdown {
  position: relative;
}

.filter-dropdown-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  /* Other styles are handled by global button classes */
}

.filter-dropdown-content {
  position: absolute;
  top: 45px;
  left: 0;
  width: 200px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 10px;
  z-index: 10;
  display: none;
}

.dark .filter-dropdown-content {
  background-color: #1e1e1e;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.filter-dropdown:hover .filter-dropdown-content {
  display: block;
}

.account-filter-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  text-align: left;
  /* Other styles are handled by global button classes */
}

.account-filter-btn img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.8rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.autoplay-toggle {
  margin-left: auto;
  /* Other styles are now handled by the AutoplayToggle component */
}

.following-content {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.following-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 30px;
}

.following-main {
  width: 100%;
}

.following-sidebar {
  position: sticky;
  top: 80px;
  height: calc(100vh - 100px);
  overflow-y: auto;
  padding-right: 10px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.video-feed {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.suggested-follows {
  background-color: var(--white);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.dark .suggested-follows {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.suggested-follows h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--text-color);
}

.dark .suggested-follows h3 {
  color: var(--white);
}

.suggested-accounts-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.suggested-account-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.account-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.account-info {
  flex: 1;
  min-width: 0;
}

.account-name {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 600;
  margin-bottom: 3px;
  color: var(--text-color);
}

.dark .account-name {
  color: var(--white);
}

.account-followers {
  font-size: 0.85rem;
  color: var(--dark-gray);
}

.dark .account-followers {
  color: #aaa;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 0;
  text-align: center;
}

.empty-state h2 {
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.empty-state p {
  color: var(--dark-gray);
}

.dark .empty-state p {
  color: #aaa;
}

/* For mobile devices */
/* Live Streams Section */
.live-streams-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color);
}

.dark .section-header h2 {
  color: var(--white);
}

.go-live-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #FF3366;
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 10px rgba(255, 51, 102, 0.3);
}

.go-live-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(255, 51, 102, 0.4);
}

.live-streams-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.live-stream-card {
  border-radius: 12px;
  overflow: hidden;
  background-color: var(--white);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dark .live-stream-card {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.live-stream-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dark .live-stream-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.live-thumbnail {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  overflow: hidden;
}

.live-thumbnail video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.live-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  justify-content: space-between;
  padding: 10px;
}

.live-badge {
  background-color: #FF3366;
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 700;
  letter-spacing: 1px;
}

.live-viewers {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(108, 19, 179, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.live-stream-card:hover .play-button {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

.live-info {
  padding: 15px;
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.streamer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.streamer-name {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 5px;
}

.dark .streamer-name {
  color: var(--white);
}

.stream-title {
  font-size: 0.9rem;
  color: var(--dark-gray);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dark .stream-title {
  color: #aaa;
}

/* Featured Video Section */
.featured-video-section {
  margin-bottom: 20px;
}

.featured-video-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  background-color: var(--white);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.dark .featured-video-container {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.featured-video-info {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.featured-user-info {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.featured-user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.featured-user-name {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 5px;
}

.dark .featured-user-name {
  color: var(--white);
}

.featured-video-caption {
  font-size: 0.95rem;
  color: var(--dark-gray);
  line-height: 1.5;
}

.dark .featured-video-caption {
  color: #aaa;
}

.featured-video-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--dark-gray);
  font-size: 0.9rem;
}

.dark .stat-item {
  color: #aaa;
}

.comment-btn {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #6C13B3;
  color: white;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .comment-btn {
  background-color: #9B4BDE;
}

.comment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(108, 19, 179, 0.3);
}

/* Live Stream Modal */
.live-stream-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

@media (max-width: 768px) {
  .following-container {
    padding: 15px;
  }

  .following-header {
    gap: 15px;
  }

  .following-filters {
    overflow-x: auto;
    padding-bottom: 10px;
    justify-content: flex-start;
    width: 100%;
  }

  .filter-btn, .filter-dropdown-btn {
    white-space: nowrap;
  }

  .autoplay-toggle {
    margin-left: 0;
  }

  .following-layout {
    grid-template-columns: 1fr;
  }

  .following-sidebar {
    position: static;
    height: auto;
    overflow-y: visible;
    padding-right: 0;
  }

  .friend-activity {
    margin-bottom: 20px;
  }

  .activity-list {
    max-height: 300px;
  }

  .live-streams-container {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .featured-video-container {
    grid-template-columns: 1fr;
  }

  .featured-video-info {
    padding: 15px;
  }

  .featured-user-avatar {
    width: 40px;
    height: 40px;
  }

  .featured-user-name {
    font-size: 1.1rem;
  }

  .featured-video-caption {
    font-size: 0.9rem;
  }

  .live-stream-modal {
    padding: 10px;
  }
}

@media (max-width: 480px) {
  .activity-tabs {
    gap: 8px;
  }

  .activity-tab {
    padding: 6px 12px;
    font-size: 0.85rem;
  }

  .activity-user-avatar {
    width: 35px;
    height: 35px;
  }

  .account-avatar {
    width: 35px;
    height: 35px;
  }

  .account-name {
    font-size: 0.9rem;
  }

  .account-followers {
    font-size: 0.8rem;
  }
}
