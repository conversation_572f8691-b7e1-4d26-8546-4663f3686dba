#root {
  width: 100%;
  height: 100%;
}

.main-container {
  display: flex;
  height: calc(100vh - 70px);
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  height: 100%;
}

/* Tablet styles */
@media (max-width: 1024px) and (min-width: 769px) {
  .content {
    padding: 15px;
  }
}

/* For mobile devices */
@media (max-width: 768px) {
  .main-container {
    flex-direction: column;
    height: calc(100vh - 70px - 70px); /* Account for top navbar and bottom nav */
  }

  .content {
    padding: 15px;
    padding-bottom: 90px; /* Extra space for bottom nav */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Desktop improvements */
@media (min-width: 1200px) {
  .content {
    padding: 25px 30px;
  }
}
