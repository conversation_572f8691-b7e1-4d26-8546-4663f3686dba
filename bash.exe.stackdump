Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBF80, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 0007FFFFBF80, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCD35C0000 ntdll.dll
7FFCD1C50000 KERNEL32.DLL
7FFCD0CC0000 KERNELBASE.dll
7FFCD2230000 USER32.dll
7FFCD1140000 win32u.dll
7FFCD1BC0000 GDI32.dll
7FFCD1210000 gdi32full.dll
7FFCD1090000 msvcp_win.dll
7FFCD0930000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCD1A40000 advapi32.dll
7FFCD18A0000 msvcrt.dll
7FFCD3170000 sechost.dll
7FFCD2FA0000 RPCRT4.dll
7FFCCFCD0000 CRYPTBASE.DLL
7FFCD1170000 bcryptPrimitives.dll
7FFCD1B00000 IMM32.DLL
