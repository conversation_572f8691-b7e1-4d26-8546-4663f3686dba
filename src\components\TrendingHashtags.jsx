import React from 'react';
import { Link } from 'react-router-dom';
import { FiHash, FiTrendingUp } from 'react-icons/fi';
import './TrendingHashtags.css';

const TrendingHashtags = ({ limit = 5 }) => {
  // Mock trending hashtags data
  const trendingHashtags = [
    { tag: 'vibevid', count: '2.5B', color: '#FF3366' },
    { tag: 'dance', count: '1.8B', color: '#00CCFF' },
    { tag: 'music', count: '1.2B', color: '#6C13B3' },
    { tag: 'comedy', count: '950M', color: '#FF9900' },
    { tag: 'food', count: '780M', color: '#33CC33' },
    { tag: 'travel', count: '650M', color: '#3366FF' },
    { tag: 'pets', count: '520M', color: '#FF6600' },
    { tag: 'fitness', count: '480M', color: '#9933CC' }
  ];

  return (
    <div className="trending-hashtags">
      <div className="trending-header">
        <FiTrendingUp className="trending-icon" />
        <h3>Trending Hashtags</h3>
      </div>
      
      <div className="hashtags-list">
        {trendingHashtags.slice(0, limit).map((hashtag, index) => (
          <Link 
            key={index} 
            to={`/explore/tag/${hashtag.tag}`}
            className="trending-tag"
            style={{ 
              '--tag-color': hashtag.color,
              animationDelay: `${index * 0.1}s`
            }}
          >
            <div className="tag-icon" style={{ backgroundColor: hashtag.color }}>
              <FiHash />
            </div>
            <div className="tag-info">
              <span className="tag-name">#{hashtag.tag}</span>
              <span className="tag-count">{hashtag.count} views</span>
            </div>
          </Link>
        ))}
      </div>
      
      <Link to="/explore/hashtags" className="see-all-link">
        See All Trending
      </Link>
    </div>
  );
};

export default TrendingHashtags;
