.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
  padding: 0 25px;
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--white);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all 0.3s ease;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.navbar.dark {
  background-color: #121212;
  color: var(--white);
  border-bottom-color: #2a2a2a;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
}

.dark .navbar-center .search-form {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.dark .search-form:focus-within {
  box-shadow: 0 2px 15px rgba(155, 75, 222, 0.2);
  border-color: rgba(155, 75, 222, 0.4);
}

.dark .navbar-center .search-form input {
  color: var(--white);
}

.dark .navbar-center .search-form input::placeholder {
  color: #aaa;
}

.dark .search-btn {
  color: #aaa;
}

.navbar-left .logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.6rem;
  font-weight: 800;
  color: #6C13B3;
  text-decoration: none;
  letter-spacing: 0.5px;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.dark .navbar-left .logo {
  color: #9B4BDE;
}

.vibevid-logo-small {
  position: relative;
  width: 36px;
  height: 36px;
  background-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
}

.wave-1-small, .wave-2-small {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  opacity: 0.8;
}

.wave-1-small {
  background-color: #FF3366;
  clip-path: circle(40% at 35% 50%);
  z-index: 1;
}

.wave-2-small {
  background-color: #00CCFF;
  clip-path: circle(40% at 65% 50%);
  z-index: 2;
}

.play-button-small {
  position: absolute;
  width: 14px;
  height: 14px;
  background-color: white;
  border-radius: 50%;
  z-index: 3;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.navbar-center {
  flex: 1;
  max-width: 500px;
  margin: 0 20px;
}

.search-form {
  display: flex;
  align-items: center;
  background-color: var(--gray);
  border-radius: 92px;
  padding: 0 16px;
  height: 44px;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--light-gray);
  transition: all 0.3s ease;
}

.search-form:focus-within {
  box-shadow: 0 2px 15px rgba(108, 19, 179, 0.15);
  border-color: rgba(108, 19, 179, 0.3);
}

.search-form input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 0 10px;
  font-size: 0.9rem;
  outline: none;
}

.search-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  cursor: pointer;
  color: var(--dark-gray);
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.upload-btn, .profile-btn, .icon-btn, .theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text-color);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .upload-btn,
.dark .profile-btn,
.dark .icon-btn,
.dark .theme-toggle {
  color: var(--white);
}

.upload-btn {
  border: 1px solid var(--light-gray);
  padding: 10px 18px;
  text-decoration: none;
  border-radius: 20px;
  background-color: #6C13B3;
  color: white;
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(108, 19, 179, 0.2);
  transition: all 0.3s ease;
}

.upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(108, 19, 179, 0.3);
}

.dark .upload-btn {
  border-color: #9B4BDE;
  background-color: #9B4BDE;
  box-shadow: 0 4px 10px rgba(155, 75, 222, 0.3);
}

.dark .upload-btn:hover {
  box-shadow: 0 6px 15px rgba(155, 75, 222, 0.4);
}

.icon-btn {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray);
  transition: all 0.3s ease;
}

.icon-btn:hover {
  background-color: var(--light-gray);
  transform: translateY(-2px);
}

.dark .icon-btn {
  background-color: #2a2a2a;
}

.dark .icon-btn:hover {
  background-color: #3a3a3a;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #FF3366;
  color: white;
  font-size: 0.7rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  box-shadow: 0 2px 5px rgba(255, 51, 102, 0.4);
  border: 2px solid white;
}

.dark .notification-badge {
  border-color: #121212;
  box-shadow: 0 2px 5px rgba(255, 51, 102, 0.6);
}

.user-menu {
  position: relative;
}

.avatar-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  padding: 0;
  border: 2px solid transparent;
  cursor: pointer;
  background: transparent;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.avatar-btn:hover {
  border-color: #6C13B3;
  transform: translateY(-2px);
}

.dark .avatar-btn:hover {
  border-color: #9B4BDE;
}

.avatar-btn img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dropdown-menu {
  position: absolute;
  top: 45px;
  right: 0;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.1);
  width: 200px;
  z-index: 10;
  overflow: hidden;
}

.dark .dropdown-menu {
  background-color: #1e1e1e;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.4);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  color: var(--text-color);
  text-decoration: none;
  transition: background-color 0.2s;
  cursor: pointer;
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  font-size: 0.9rem;
}

.dark .dropdown-item {
  color: var(--white);
}

.dropdown-item:hover {
  background-color: var(--gray);
}

.dark .dropdown-item:hover {
  background-color: #2a2a2a;
}

.dropdown-item.logout {
  border-top: 1px solid var(--light-gray);
  color: #FF3366;
}

.dark .dropdown-item.logout {
  border-top-color: #2a2a2a;
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: none;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: var(--gray);
  border: none;
  color: var(--text-color);
  cursor: pointer;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  transition: all 0.3s ease;
}

.dark .mobile-menu-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.mobile-menu-btn:hover {
  background-color: var(--light-gray);
  transform: translateY(-2px);
}

.dark .mobile-menu-btn:hover {
  background-color: #3a3a3a;
}

/* Mobile Search Button */
.mobile-search-btn {
  display: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--gray);
  border: none;
  color: var(--text-color);
  cursor: pointer;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.dark .mobile-search-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.mobile-search-btn:hover {
  background-color: var(--light-gray);
  transform: translateY(-2px);
}

.dark .mobile-search-btn:hover {
  background-color: #3a3a3a;
}

/* Mobile Search Overlay */
.mobile-search-overlay {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  background-color: var(--white);
  border-bottom: 1px solid var(--light-gray);
  z-index: 1000;
  padding: 15px 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.dark .mobile-search-overlay {
  background-color: #121212;
  border-bottom-color: #2a2a2a;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.mobile-search-form {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: var(--gray);
  border-radius: 25px;
  padding: 0 15px;
  height: 50px;
  border: 1px solid var(--light-gray);
}

.dark .mobile-search-form {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
}

.mobile-search-form input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 0;
  font-size: 1rem;
  outline: none;
  color: var(--text-color);
}

.dark .mobile-search-form input {
  color: var(--white);
}

.mobile-search-form input::placeholder {
  color: var(--dark-gray);
}

.dark .mobile-search-form input::placeholder {
  color: #aaa;
}

.mobile-search-submit,
.mobile-search-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: transparent;
  color: var(--dark-gray);
  cursor: pointer;
  transition: all 0.3s ease;
}

.dark .mobile-search-submit,
.dark .mobile-search-close {
  color: #aaa;
}

.mobile-search-submit:hover,
.mobile-search-close:hover {
  background-color: var(--light-gray);
  color: var(--text-color);
}

.dark .mobile-search-submit:hover,
.dark .mobile-search-close:hover {
  background-color: #3a3a3a;
  color: var(--white);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.mobile-menu {
  background-color: var(--white);
  height: 100%;
  width: 280px;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.dark .mobile-menu {
  background-color: #121212;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .mobile-menu-header {
  border-bottom-color: #2a2a2a;
}

.mobile-menu-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-color);
}

.dark .mobile-menu-header h3 {
  color: var(--white);
}

.mobile-menu-close {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background-color: var(--gray);
  color: var(--text-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.dark .mobile-menu-close {
  background-color: #2a2a2a;
  color: var(--white);
}

.mobile-menu-close:hover {
  background-color: var(--light-gray);
}

.dark .mobile-menu-close:hover {
  background-color: #3a3a3a;
}

.mobile-menu-content {
  padding: 20px 0;
}

.mobile-nav-section {
  margin-bottom: 30px;
}

.mobile-nav-section h4 {
  margin: 0 0 15px 20px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--dark-gray);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.dark .mobile-nav-section h4 {
  color: #aaa;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  color: var(--text-color);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  cursor: pointer;
  position: relative;
}

.dark .mobile-nav-item {
  color: var(--white);
}

.mobile-nav-item:hover {
  background-color: var(--gray);
  padding-left: 25px;
}

.dark .mobile-nav-item:hover {
  background-color: #2a2a2a;
}

.mobile-nav-item.active {
  background-color: rgba(108, 19, 179, 0.1);
  color: #6C13B3;
  border-right: 3px solid #6C13B3;
}

.dark .mobile-nav-item.active {
  background-color: rgba(155, 75, 222, 0.2);
  color: #9B4BDE;
  border-right-color: #9B4BDE;
}

.mobile-nav-item.logout {
  color: #FF3366;
  border-top: 1px solid var(--light-gray);
  margin-top: 10px;
}

.dark .mobile-nav-item.logout {
  border-top-color: #2a2a2a;
}

.mobile-notification-badge {
  background-color: #FF3366;
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
  font-weight: 700;
}

/* For mobile devices */
@media (max-width: 768px) {
  .navbar {
    padding: 0 15px;
  }

  .mobile-menu-btn,
  .mobile-search-btn {
    display: flex;
  }

  .navbar-center {
    display: none;
  }

  .logo-text {
    display: none;
  }

  .desktop-only {
    display: none;
  }

  .upload-text {
    display: none;
  }

  .upload-btn {
    padding: 10px;
    min-width: 40px;
  }

  .navbar-right {
    gap: 8px;
  }
}
