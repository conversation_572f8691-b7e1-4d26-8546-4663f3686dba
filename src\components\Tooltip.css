.tooltip-container {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
}

.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 14px;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s, transform 0.3s;
  transform: translateX(-50%) translateY(10px);
  pointer-events: none;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
  margin-bottom: 12px;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -8px;
  border-width: 8px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
}

.tooltip-container:hover .tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* Tooltip positions */
.tooltip.top {
  bottom: 100%;
  margin-bottom: 10px;
}

.tooltip.bottom {
  top: 100%;
  bottom: auto;
  margin-top: 10px;
}

.tooltip.bottom::after {
  top: auto;
  bottom: 100%;
  border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
}

.tooltip.left {
  right: 100%;
  left: auto;
  top: 50%;
  bottom: auto;
  transform: translateY(-50%) translateX(-10px);
  margin-right: 10px;
}

.tooltip.left::after {
  top: 50%;
  left: 100%;
  margin-left: 0;
  margin-top: -5px;
  border-color: transparent transparent transparent rgba(0, 0, 0, 0.8);
}

.tooltip.right {
  left: 100%;
  right: auto;
  top: 50%;
  bottom: auto;
  transform: translateY(-50%) translateX(10px);
  margin-left: 10px;
}

.tooltip.right::after {
  top: 50%;
  right: 100%;
  left: auto;
  margin-top: -5px;
  border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent;
}

.tooltip-container:hover .tooltip.left,
.tooltip-container:hover .tooltip.right {
  transform: translateY(-50%) translateX(0);
}

/* Dark mode */
.dark .tooltip {
  background-color: rgba(40, 40, 40, 0.95);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
}

.dark .tooltip::after {
  border-color: rgba(40, 40, 40, 0.95) transparent transparent transparent;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
}

.dark .tooltip.bottom::after {
  border-color: transparent transparent rgba(40, 40, 40, 0.95) transparent;
}

.dark .tooltip.left::after {
  border-color: transparent transparent transparent rgba(40, 40, 40, 0.95);
}

.dark .tooltip.right::after {
  border-color: transparent rgba(40, 40, 40, 0.95) transparent transparent;
}
