import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { FiEdit, FiShare2, FiGrid, FiHeart, FiLock, FiUserPlus, FiUserCheck, FiMessageSquare, FiMoreHorizontal, FiBookmark, FiBarChart2, FiAward, FiTag, FiClock, FiVideo, FiMusic, FiGlobe, FiMail, FiInstagram, FiTwitter, FiYoutube, FiMapPin, FiCalendar, FiLink, FiPlay } from 'react-icons/fi';
import { FaFacebook as FiFacebook } from 'react-icons/fa';
import './Profile.css';

const Profile = () => {
  const { username } = useParams();
  const [activeTab, setActiveTab] = useState('videos');
  const [isFollowing, setIsFollowing] = useState(false);
  const [showMoreOptions, setShowMoreOptions] = useState(false);
  const [isCurrentUser, setIsCurrentUser] = useState(false);
  const [showShareOptions, setShowShareOptions] = useState(false);
  const [showBadges, setShowBadges] = useState(false);

  useEffect(() => {
    // In a real app, you would fetch the user data from an API
    // and check if this is the current user's profile
    if (username === 'user123') {
      setIsCurrentUser(true);
    }
  }, [username]);

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
  };

  // Mock user data
  const user = {
    username: username,
    name: 'Alex Johnson',
    avatar: 'https://placehold.co/150',
    coverPhoto: 'https://placehold.co/1200x300',
    bio: 'Creating vibrant content daily ✨ | Music lover 🎵 | Travel enthusiast 🌍 | #VibeVid creator',
    website: 'www.alexjohnson.com',
    location: 'Los Angeles, CA',
    joinDate: 'Joined June 2023',
    following: 123,
    followers: 45600,
    likes: 789000,
    verified: true,
    badges: [
      { id: 1, name: 'Top Creator', icon: <FiAward />, color: '#FF3366' },
      { id: 2, name: 'Trending', icon: <FiTag />, color: '#00CCFF' },
      { id: 3, name: 'Early Adopter', icon: <FiClock />, color: '#6C13B3' }
    ],
    socialLinks: [
      { platform: 'Instagram', url: 'https://instagram.com/alexjohnson', icon: <FiInstagram /> },
      { platform: 'Twitter', url: 'https://twitter.com/alexjohnson', icon: <FiTwitter /> },
      { platform: 'YouTube', url: 'https://youtube.com/alexjohnson', icon: <FiYoutube /> }
    ],
    email: '<EMAIL>',
    videos: [
      {
        id: 1,
        thumbnail: 'https://placehold.co/300x400/FF3366/FFFFFF?text=Sunset+Vibes',
        views: '1.2M',
        likes: '342K',
        comments: '2.1K',
        description: 'Sunset vibes at the beach 🌅 #sunset #beach',
        date: '2 days ago'
      },
      {
        id: 2,
        thumbnail: 'https://placehold.co/300x400/00CCFF/FFFFFF?text=City+Lights',
        views: '456K',
        likes: '98K',
        comments: '876',
        description: 'City lights and night drives 🌃 #city #night',
        date: '1 week ago'
      },
      {
        id: 3,
        thumbnail: 'https://placehold.co/300x400/6C13B3/FFFFFF?text=Dance+Moves',
        views: '789K',
        likes: '156K',
        comments: '1.3K',
        description: 'New dance challenge! Try it out 💃 #dance #challenge',
        date: '2 weeks ago'
      },
      {
        id: 4,
        thumbnail: 'https://placehold.co/300x400/FF9900/FFFFFF?text=Food+Tour',
        views: '123K',
        likes: '45K',
        comments: '567',
        description: 'Food tour in Tokyo 🍣 #food #tokyo',
        date: '3 weeks ago'
      },
      {
        id: 5,
        thumbnail: 'https://placehold.co/300x400/33CC33/FFFFFF?text=Nature+Walk',
        views: '45K',
        likes: '12K',
        comments: '234',
        description: 'Nature walk in the forest 🌲 #nature #hiking',
        date: '1 month ago'
      },
      {
        id: 6,
        thumbnail: 'https://placehold.co/300x400/9933CC/FFFFFF?text=Music+Session',
        views: '6.7K',
        likes: '2.1K',
        comments: '98',
        description: 'Jamming session with friends 🎸 #music #jam',
        date: '1 month ago'
      }
    ],
    likedVideos: [
      {
        id: 7,
        thumbnail: 'https://placehold.co/300x400/FF6600/FFFFFF?text=Travel+Vlog',
        views: '89K',
        likes: '23K',
        comments: '456',
        description: 'Travel vlog in Bali 🏝️ #travel #bali',
        date: '2 months ago'
      },
      {
        id: 8,
        thumbnail: 'https://placehold.co/300x400/3366FF/FFFFFF?text=Workout+Routine',
        views: '12K',
        likes: '3.4K',
        comments: '78',
        description: 'My daily workout routine 💪 #fitness #workout',
        date: '2 months ago'
      }
    ],
    savedVideos: [
      {
        id: 9,
        thumbnail: 'https://placehold.co/300x400/FF33CC/FFFFFF?text=DIY+Project',
        views: '34K',
        likes: '8.9K',
        comments: '345',
        description: 'DIY home decor project 🏠 #diy #homedecor',
        date: '3 months ago'
      }
    ]
  };

  return (
    <div className="profile-container">
      <div className="profile-cover" style={{ backgroundImage: `url(${user.coverPhoto})` }}>
        <div className="cover-overlay"></div>
      </div>

      <div className="profile-header">
        <div className="profile-info">
          <div className="profile-avatar-container">
            <img src={user.avatar} alt={user.username} className="profile-avatar" />
          </div>
          <div className="profile-details">
            <div className="profile-name-section">
              <div className="username-container">
                <h2 className="profile-username">@{user.username}</h2>
                {user.verified && (
                  <span className="verified-badge" title="Verified Account">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#6C13B3" width="20" height="20">
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
                    </svg>
                  </span>
                )}
              </div>
              <h3 className="profile-name">{user.name}</h3>

              <div className="user-badges">
                {user.badges.map(badge => (
                  <div
                    key={badge.id}
                    className="user-badge"
                    style={{ backgroundColor: badge.color + '20', color: badge.color }}
                    title={badge.name}
                  >
                    {badge.icon}
                    <span>{badge.name}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="profile-stats">
              <div className="stat">
                <span className="stat-count">{user.following.toLocaleString()}</span>
                <span className="stat-label">Following</span>
              </div>
              <div className="stat">
                <span className="stat-count">{user.followers.toLocaleString()}</span>
                <span className="stat-label">Followers</span>
              </div>
              <div className="stat">
                <span className="stat-count">{user.likes.toLocaleString()}</span>
                <span className="stat-label">Likes</span>
              </div>
            </div>

            <p className="profile-bio">{user.bio}</p>

            <div className="profile-meta">
              {user.website && (
                <a href={`https://${user.website}`} target="_blank" rel="noopener noreferrer" className="profile-website">
                  <FiGlobe /> {user.website}
                </a>
              )}
              {user.location && <span className="profile-location"><FiMapPin /> {user.location}</span>}
              <span className="profile-join-date"><FiCalendar /> {user.joinDate}</span>
              {user.email && <span className="profile-email"><FiMail /> {user.email}</span>}
            </div>

            <div className="social-links">
              {user.socialLinks.map(social => (
                <a
                  key={social.platform}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="social-link"
                  title={social.platform}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>
        </div>

        <div className="profile-actions">
          {isCurrentUser ? (
            <>
              <Link to="/settings" className="edit-profile-btn">
                <FiEdit />
                <span>Edit Profile</span>
              </Link>
            </>
          ) : (
            <>
              <button className={`follow-btn ${isFollowing ? 'following' : ''}`} onClick={handleFollow}>
                {isFollowing ? <><FiUserCheck /> <span>Following</span></> : <><FiUserPlus /> <span>Follow</span></>}
              </button>
              <button className="message-btn">
                <FiMessageSquare />
              </button>
            </>
          )}

          <div className="share-profile-container">
            <button className="share-profile-btn" onClick={() => setShowShareOptions(!showShareOptions)}>
              <FiShare2 />
            </button>

            {showShareOptions && (
              <div className="share-options-dropdown">
                <button className="share-option" onClick={() => {
                  navigator.clipboard.writeText(window.location.href);
                  alert('Profile link copied to clipboard!');
                }}>
                  <FiLink /> Copy Link
                </button>
                <a className="share-option" href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=Check out ${user.name}'s profile on VibeVid!`} target="_blank" rel="noopener noreferrer">
                  <FiTwitter /> Share on Twitter
                </a>
                <a className="share-option" href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`} target="_blank" rel="noopener noreferrer">
                  <FiFacebook /> Share on Facebook
                </a>
              </div>
            )}
          </div>

          <div className="more-options">
            <button className="more-btn" onClick={() => setShowMoreOptions(!showMoreOptions)}>
              <FiMoreHorizontal />
            </button>

            {showMoreOptions && (
              <div className="options-dropdown">
                <button className="option-item">Report User</button>
                <button className="option-item">Block User</button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="profile-tabs">
        <button
          className={`tab-btn ${activeTab === 'videos' ? 'active' : ''}`}
          onClick={() => setActiveTab('videos')}
        >
          <FiGrid />
          <span>Videos</span>
        </button>
        <button
          className={`tab-btn ${activeTab === 'liked' ? 'active' : ''}`}
          onClick={() => setActiveTab('liked')}
        >
          <FiHeart />
          <span>Liked</span>
        </button>
        <button
          className={`tab-btn ${activeTab === 'saved' ? 'active' : ''}`}
          onClick={() => setActiveTab('saved')}
        >
          <FiBookmark />
          <span>Saved</span>
        </button>
        <button
          className={`tab-btn ${activeTab === 'music' ? 'active' : ''}`}
          onClick={() => setActiveTab('music')}
        >
          <FiMusic />
          <span>Music</span>
        </button>
        <button
          className={`tab-btn ${activeTab === 'tagged' ? 'active' : ''}`}
          onClick={() => setActiveTab('tagged')}
        >
          <FiTag />
          <span>Tagged</span>
        </button>
        {isCurrentUser && (
          <>
            <button
              className={`tab-btn ${activeTab === 'drafts' ? 'active' : ''}`}
              onClick={() => setActiveTab('drafts')}
            >
              <FiClock />
              <span>Drafts</span>
            </button>
            <button
              className={`tab-btn ${activeTab === 'analytics' ? 'active' : ''}`}
              onClick={() => setActiveTab('analytics')}
            >
              <FiBarChart2 />
              <span>Analytics</span>
            </button>
          </>
        )}
      </div>

      <div className="profile-content">
        {activeTab === 'videos' && (
          <div className="video-grid">
            {user.videos.map(video => (
              <div key={video.id} className="video-item">
                <div className="thumbnail-container">
                  <img src={video.thumbnail} alt={`Video ${video.id}`} />
                  <div className="video-overlay">
                    <div className="video-stats">
                      <div className="video-stat">
                        <FiHeart />
                        <span>{video.likes}</span>
                      </div>
                      <div className="video-stat">
                        <FiMessageSquare />
                        <span>{video.comments}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="video-info">
                  <p className="video-description">{video.description}</p>
                  <span className="video-date">{video.date}</span>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'liked' && (
          <div className="video-grid">
            {user.likedVideos.map(video => (
              <div key={video.id} className="video-item">
                <div className="thumbnail-container">
                  <img src={video.thumbnail} alt={`Video ${video.id}`} />
                  <div className="video-overlay">
                    <div className="video-stats">
                      <div className="video-stat">
                        <FiHeart />
                        <span>{video.likes}</span>
                      </div>
                      <div className="video-stat">
                        <FiMessageSquare />
                        <span>{video.comments}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="video-info">
                  <p className="video-description">{video.description}</p>
                  <span className="video-date">{video.date}</span>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'saved' && (
          <div className="video-grid">
            {user.savedVideos.map(video => (
              <div key={video.id} className="video-item">
                <div className="thumbnail-container">
                  <img src={video.thumbnail} alt={`Video ${video.id}`} />
                  <div className="video-overlay">
                    <div className="video-stats">
                      <div className="video-stat">
                        <FiHeart />
                        <span>{video.likes}</span>
                      </div>
                      <div className="video-stat">
                        <FiMessageSquare />
                        <span>{video.comments}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="video-info">
                  <p className="video-description">{video.description}</p>
                  <span className="video-date">{video.date}</span>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'music' && (
          <div className="music-container">
            <h3 className="section-title">Favorite Music</h3>
            <div className="music-grid">
              {[
                { id: 1, title: 'Summer Vibes', artist: 'DJ Sunshine', cover: 'https://placehold.co/300x300/FF3366/FFFFFF?text=Summer+Vibes', usedCount: 45 },
                { id: 2, title: 'Chill Beats', artist: 'Lo-Fi Dreams', cover: 'https://placehold.co/300x300/00CCFF/FFFFFF?text=Chill+Beats', usedCount: 32 },
                { id: 3, title: 'Dance Party', artist: 'Groove Masters', cover: 'https://placehold.co/300x300/6C13B3/FFFFFF?text=Dance+Party', usedCount: 28 },
                { id: 4, title: 'Acoustic Sessions', artist: 'Melody Makers', cover: 'https://placehold.co/300x300/FF9900/FFFFFF?text=Acoustic', usedCount: 19 },
                { id: 5, title: 'Electronic Dreams', artist: 'Synth Wave', cover: 'https://placehold.co/300x300/33CC33/FFFFFF?text=Electronic', usedCount: 15 },
                { id: 6, title: 'Hip Hop Classics', artist: 'Beat Droppers', cover: 'https://placehold.co/300x300/9933CC/FFFFFF?text=Hip+Hop', usedCount: 12 }
              ].map(song => (
                <div key={song.id} className="music-item">
                  <div className="music-cover">
                    <img src={song.cover} alt={song.title} />
                    <div className="play-overlay">
                      <FiPlay size={24} />
                    </div>
                  </div>
                  <div className="music-info">
                    <h4 className="music-title">{song.title}</h4>
                    <p className="music-artist">{song.artist}</p>
                    <span className="music-used">Used in {song.usedCount} videos</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'tagged' && (
          <div className="tagged-container">
            <h3 className="section-title">Tagged Videos</h3>
            <div className="video-grid">
              {[
                { id: 101, thumbnail: 'https://placehold.co/300x400/FF3366/FFFFFF?text=Tagged+1', views: '56K', likes: '12K', comments: '234', description: 'Collab with @alexjohnson 🤩 #collab #fun', date: '1 week ago' },
                { id: 102, thumbnail: 'https://placehold.co/300x400/00CCFF/FFFFFF?text=Tagged+2', views: '34K', likes: '8.9K', comments: '156', description: 'Thanks for the shoutout @alexjohnson! 🙏 #shoutout', date: '2 weeks ago' },
                { id: 103, thumbnail: 'https://placehold.co/300x400/6C13B3/FFFFFF?text=Tagged+3', views: '78K', likes: '23K', comments: '345', description: 'Dance challenge with @alexjohnson 💃 #dancechallenge', date: '3 weeks ago' },
                { id: 104, thumbnail: 'https://placehold.co/300x400/FF9900/FFFFFF?text=Tagged+4', views: '12K', likes: '3.4K', comments: '67', description: 'Q&A session with @alexjohnson ❓ #QandA', date: '1 month ago' }
              ].map(video => (
                <div key={video.id} className="video-item">
                  <div className="thumbnail-container">
                    <img src={video.thumbnail} alt={`Video ${video.id}`} />
                    <div className="video-overlay">
                      <div className="video-stats">
                        <div className="video-stat">
                          <FiHeart />
                          <span>{video.likes}</span>
                        </div>
                        <div className="video-stat">
                          <FiMessageSquare />
                          <span>{video.comments}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="video-info">
                    <p className="video-description">{video.description}</p>
                    <span className="video-date">{video.date}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'drafts' && isCurrentUser && (
          <div className="drafts-container">
            <h3 className="section-title">Draft Videos</h3>
            <div className="video-grid">
              {[
                { id: 201, thumbnail: 'https://placehold.co/300x400/FF3366/FFFFFF?text=Draft+1', duration: '0:45', description: 'New dance routine - needs editing 💃', date: 'Created 2 days ago' },
                { id: 202, thumbnail: 'https://placehold.co/300x400/00CCFF/FFFFFF?text=Draft+2', duration: '1:20', description: 'Cooking tutorial - add music 🍳', date: 'Created 5 days ago' },
                { id: 203, thumbnail: 'https://placehold.co/300x400/6C13B3/FFFFFF?text=Draft+3', duration: '0:30', description: 'Travel vlog intro - needs captions 🌍', date: 'Created 1 week ago' }
              ].map(draft => (
                <div key={draft.id} className="draft-item">
                  <div className="thumbnail-container">
                    <img src={draft.thumbnail} alt={`Draft ${draft.id}`} />
                    <div className="draft-overlay">
                      <div className="draft-duration">{draft.duration}</div>
                      <div className="draft-actions">
                        <button className="draft-action edit"><FiEdit /> Edit</button>
                        <button className="draft-action publish"><FiVideo /> Publish</button>
                      </div>
                    </div>
                  </div>
                  <div className="draft-info">
                    <p className="draft-description">{draft.description}</p>
                    <span className="draft-date">{draft.date}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'analytics' && isCurrentUser && (
          <div className="analytics-container">
            <div className="analytics-header">
              <h3>Video Performance</h3>
              <select className="time-filter">
                <option value="7days">Last 7 days</option>
                <option value="30days">Last 30 days</option>
                <option value="90days">Last 90 days</option>
              </select>
            </div>

            <div className="analytics-cards">
              <div className="analytics-card">
                <h4>Profile Views</h4>
                <div className="analytics-value">12.5K</div>
                <div className="analytics-change positive">+15.2%</div>
              </div>

              <div className="analytics-card">
                <h4>Video Views</h4>
                <div className="analytics-value">345.8K</div>
                <div className="analytics-change positive">+23.7%</div>
              </div>

              <div className="analytics-card">
                <h4>Likes</h4>
                <div className="analytics-value">78.2K</div>
                <div className="analytics-change positive">+18.4%</div>
              </div>

              <div className="analytics-card">
                <h4>Comments</h4>
                <div className="analytics-value">5.6K</div>
                <div className="analytics-change negative">-2.1%</div>
              </div>
            </div>

            <div className="analytics-charts">
              <div className="analytics-chart">
                <h4>Audience Demographics</h4>
                <div className="chart-placeholder" style={{ height: '200px', background: 'rgba(108, 19, 179, 0.1)', borderRadius: '10px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <span>Demographics Chart</span>
                </div>
              </div>
              <div className="analytics-chart">
                <h4>Content Performance</h4>
                <div className="chart-placeholder" style={{ height: '200px', background: 'rgba(255, 51, 102, 0.1)', borderRadius: '10px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <span>Performance Chart</span>
                </div>
              </div>
            </div>

            <div className="top-videos">
              <h3>Top Performing Videos</h3>
              <div className="top-videos-list">
                {user.videos.slice(0, 3).map(video => (
                  <div key={video.id} className="top-video-item">
                    <img src={video.thumbnail} alt={`Video ${video.id}`} className="top-video-thumbnail" />
                    <div className="top-video-info">
                      <p className="top-video-description">{video.description}</p>
                      <div className="top-video-stats">
                        <span>{video.views} views</span>
                        <span>{video.likes} likes</span>
                        <span>{video.comments} comments</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;
