import { useState, useEffect } from 'react';
import './SplashScreen.css';

const SplashScreen = ({ onFinish }) => {
  const [fadeOut, setFadeOut] = useState(false);

  useEffect(() => {
    // Start fade out after 2 seconds
    const fadeTimer = setTimeout(() => {
      setFadeOut(true);
    }, 2000);

    // Complete splash screen after fade out animation (total 2.5 seconds)
    const finishTimer = setTimeout(() => {
      onFinish();
    }, 2500);

    return () => {
      clearTimeout(fadeTimer);
      clearTimeout(finishTimer);
    };
  }, [onFinish]);

  return (
    <div className={`splash-screen ${fadeOut ? 'fade-out' : ''}`}>
      <div className="splash-content">
        <div className="logo-container">
          <div className="vibevid-logo">
            <div className="wave-1"></div>
            <div className="wave-2"></div>
            <div className="play-button">
              <div className="play-icon"></div>
            </div>
          </div>
        </div>
        <h1 className="app-name">VibeVid</h1>
        <p className="app-tagline">Share your vibe</p>
      </div>
    </div>
  );
};

export default SplashScreen;
