import React, { useState } from 'react';
import { FiHash, FiArrowLeft, FiTrendingUp, FiFilter } from 'react-icons/fi';
import { Link, useNavigate } from 'react-router-dom';
import './Explore.css';

const ExploreHashtags = () => {
  const [activeFilter, setActiveFilter] = useState('trending');
  const navigate = useNavigate();

  // Mock trending hashtags with more details
  const trendingHashtags = [
    { 
      tag: 'vibevid', 
      count: '2.5B', 
      color: '#FF3366',
      description: 'The official VibeVid hashtag for trending content',
      videoCount: 1250000,
      recentVideos: [
        'https://placehold.co/300x400/FF3366/FFFFFF?text=VibeVid+1',
        'https://placehold.co/300x400/FF3366/FFFFFF?text=VibeVid+2',
        'https://placehold.co/300x400/FF3366/FFFFFF?text=VibeVid+3'
      ]
    },
    { 
      tag: 'dance', 
      count: '1.8B', 
      color: '#00CCFF',
      description: 'Show off your best dance moves and choreography',
      videoCount: 980000,
      recentVideos: [
        'https://placehold.co/300x400/00CCFF/FFFFFF?text=Dance+1',
        'https://placehold.co/300x400/00CCFF/FFFFFF?text=Dance+2',
        'https://placehold.co/300x400/00CCFF/FFFFFF?text=Dance+3'
      ]
    },
    { 
      tag: 'music', 
      count: '1.2B', 
      color: '#6C13B3',
      description: 'Share your musical talents and favorite songs',
      videoCount: 750000,
      recentVideos: [
        'https://placehold.co/300x400/6C13B3/FFFFFF?text=Music+1',
        'https://placehold.co/300x400/6C13B3/FFFFFF?text=Music+2',
        'https://placehold.co/300x400/6C13B3/FFFFFF?text=Music+3'
      ]
    },
    { 
      tag: 'comedy', 
      count: '950M', 
      color: '#FF9900',
      description: 'Funny videos to make everyone laugh',
      videoCount: 620000,
      recentVideos: [
        'https://placehold.co/300x400/FF9900/FFFFFF?text=Comedy+1',
        'https://placehold.co/300x400/FF9900/FFFFFF?text=Comedy+2',
        'https://placehold.co/300x400/FF9900/FFFFFF?text=Comedy+3'
      ]
    },
    { 
      tag: 'food', 
      count: '780M', 
      color: '#33CC33',
      description: 'Delicious recipes and food inspiration',
      videoCount: 540000,
      recentVideos: [
        'https://placehold.co/300x400/33CC33/FFFFFF?text=Food+1',
        'https://placehold.co/300x400/33CC33/FFFFFF?text=Food+2',
        'https://placehold.co/300x400/33CC33/FFFFFF?text=Food+3'
      ]
    },
    { 
      tag: 'travel', 
      count: '650M', 
      color: '#3366FF',
      description: 'Explore amazing destinations around the world',
      videoCount: 420000,
      recentVideos: [
        'https://placehold.co/300x400/3366FF/FFFFFF?text=Travel+1',
        'https://placehold.co/300x400/3366FF/FFFFFF?text=Travel+2',
        'https://placehold.co/300x400/3366FF/FFFFFF?text=Travel+3'
      ]
    },
    { 
      tag: 'pets', 
      count: '520M', 
      color: '#FF6600',
      description: 'Cute and funny pet videos',
      videoCount: 380000,
      recentVideos: [
        'https://placehold.co/300x400/FF6600/FFFFFF?text=Pets+1',
        'https://placehold.co/300x400/FF6600/FFFFFF?text=Pets+2',
        'https://placehold.co/300x400/FF6600/FFFFFF?text=Pets+3'
      ]
    },
    { 
      tag: 'fitness', 
      count: '480M', 
      color: '#9933CC',
      description: 'Workout tips and fitness motivation',
      videoCount: 320000,
      recentVideos: [
        'https://placehold.co/300x400/9933CC/FFFFFF?text=Fitness+1',
        'https://placehold.co/300x400/9933CC/FFFFFF?text=Fitness+2',
        'https://placehold.co/300x400/9933CC/FFFFFF?text=Fitness+3'
      ]
    },
    { 
      tag: 'beauty', 
      count: '420M', 
      color: '#FF66CC',
      description: 'Beauty tips, makeup tutorials and skincare routines',
      videoCount: 290000,
      recentVideos: [
        'https://placehold.co/300x400/FF66CC/FFFFFF?text=Beauty+1',
        'https://placehold.co/300x400/FF66CC/FFFFFF?text=Beauty+2',
        'https://placehold.co/300x400/FF66CC/FFFFFF?text=Beauty+3'
      ]
    },
    { 
      tag: 'diy', 
      count: '380M', 
      color: '#66CCCC',
      description: 'Do-it-yourself projects and crafts',
      videoCount: 250000,
      recentVideos: [
        'https://placehold.co/300x400/66CCCC/FFFFFF?text=DIY+1',
        'https://placehold.co/300x400/66CCCC/FFFFFF?text=DIY+2',
        'https://placehold.co/300x400/66CCCC/FFFFFF?text=DIY+3'
      ]
    },
    { 
      tag: 'gaming', 
      count: '350M', 
      color: '#00CC99',
      description: 'Gaming highlights and walkthroughs',
      videoCount: 230000,
      recentVideos: [
        'https://placehold.co/300x400/00CC99/FFFFFF?text=Gaming+1',
        'https://placehold.co/300x400/00CC99/FFFFFF?text=Gaming+2',
        'https://placehold.co/300x400/00CC99/FFFFFF?text=Gaming+3'
      ]
    },
    { 
      tag: 'fashion', 
      count: '320M', 
      color: '#CC99FF',
      description: 'Fashion trends and style inspiration',
      videoCount: 210000,
      recentVideos: [
        'https://placehold.co/300x400/CC99FF/FFFFFF?text=Fashion+1',
        'https://placehold.co/300x400/CC99FF/FFFFFF?text=Fashion+2',
        'https://placehold.co/300x400/CC99FF/FFFFFF?text=Fashion+3'
      ]
    }
  ];

  // Filter options
  const filters = [
    { id: 'trending', name: 'Trending', icon: <FiTrendingUp /> },
    { id: 'newest', name: 'Newest', icon: <FiFilter /> },
    { id: 'popular', name: 'Most Popular', icon: <FiFilter /> }
  ];

  return (
    <div className="explore-container">
      <div className="explore-header">
        <button className="back-btn" onClick={() => navigate(-1)}>
          <FiArrowLeft />
        </button>
        <h1>Trending Hashtags</h1>
      </div>

      <div className="filter-tabs">
        {filters.map(filter => (
          <button
            key={filter.id}
            className={`filter-tab ${activeFilter === filter.id ? 'active' : ''}`}
            onClick={() => setActiveFilter(filter.id)}
          >
            {filter.icon}
            <span>{filter.name}</span>
          </button>
        ))}
      </div>

      <div className="hashtags-detailed-grid">
        {trendingHashtags.map(hashtag => (
          <Link
            key={hashtag.tag}
            to={`/explore/tag/${hashtag.tag}`}
            className="hashtag-detailed-card"
          >
            <div className="hashtag-header" style={{ backgroundColor: hashtag.color }}>
              <div className="hashtag-icon">
                <FiHash color="white" size={24} />
              </div>
              <h2>#{hashtag.tag}</h2>
            </div>
            
            <div className="hashtag-content">
              <div className="hashtag-stats">
                <div className="stat">
                  <span className="stat-value">{hashtag.count}</span>
                  <span className="stat-label">Views</span>
                </div>
                <div className="stat">
                  <span className="stat-value">{hashtag.videoCount.toLocaleString()}</span>
                  <span className="stat-label">Videos</span>
                </div>
              </div>
              
              <p className="hashtag-description">{hashtag.description}</p>
              
              <div className="hashtag-preview">
                {hashtag.recentVideos.map((video, index) => (
                  <div key={index} className="preview-thumbnail">
                    <img src={video} alt={`${hashtag.tag} video ${index + 1}`} />
                  </div>
                ))}
              </div>
              
              <button className="view-hashtag-btn" style={{ backgroundColor: hashtag.color }}>
                View Videos
              </button>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default ExploreHashtags;
