import React, { useState, useContext } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FiHome, FiUsers, FiCompass, FiVideo, FiTrendingUp, FiMusic, FiRadio } from 'react-icons/fi';
import { ThemeContext } from '../contexts/ThemeContext';
import './Sidebar.css';

function Sidebar() {
  const { isDarkMode } = useContext(ThemeContext);
  const location = useLocation();

  // Mock suggested accounts
  const suggestedAccounts = [
    { id: 1, username: 'alex_johnson', name: '<PERSON>', avatar: 'https://randomuser.me/api/portraits/men/32.jpg', isVerified: true },
    { id: 2, username: 'emma_wilson', name: '<PERSON>', avatar: 'https://randomuser.me/api/portraits/women/44.jpg', isVerified: true },
    { id: 3, username: 'mi<PERSON><PERSON>_brown', name: '<PERSON>', avatar: 'https://randomuser.me/api/portraits/men/22.jpg', isVerified: false },
  ];

  // Mock following accounts
  const followingAccounts = [
    { id: 4, username: 'sophia_<PERSON>arcia', name: '<PERSON>', avatar: 'https://randomuser.me/api/portraits/women/56.jpg', isVerified: true },
    { id: 5, username: 'daniel_lee', name: 'Daniel Lee', avatar: 'https://randomuser.me/api/portraits/men/45.jpg', isVerified: false },
  ];

  // Mock trending hashtags
  const trendingHashtags = [
    { tag: 'vibevid', count: '2.5B' },
    { tag: 'dance', count: '1.8B' },
    { tag: 'music', count: '1.2B' },
  ];

  const [showMore, setShowMore] = useState(false);

  return (
    <div className={`sidebar ${isDarkMode ? 'dark' : ''}`}>
      <div className="sidebar-menu">
        <Link to="/" className={`sidebar-item ${location.pathname === '/' ? 'active' : ''}`}>
          <FiHome />
          <span>For You</span>
        </Link>
        <Link to="/following" className={`sidebar-item ${location.pathname === '/following' ? 'active' : ''}`}>
          <FiUsers />
          <span>Following</span>
        </Link>
        <Link to="/explore" className={`sidebar-item ${location.pathname === '/explore' ? 'active' : ''}`}>
          <FiCompass />
          <span>Explore</span>
        </Link>
        <Link to="/live" className={`sidebar-item ${location.pathname === '/live' ? 'active' : ''}`}>
          <FiRadio />
          <span>LIVE</span>
        </Link>
      </div>

      <div className="sidebar-section">
        <h3>Suggested accounts</h3>
        <div className="account-list">
          {suggestedAccounts.map(account => (
            <Link
              key={account.id}
              to={`/profile/${account.username}`}
              className="account-item"
            >
              <img src={account.avatar} alt={account.username} />
              <div className="account-info">
                <div className="account-name-container">
                  <span className="name">{account.name}</span>
                  {account.isVerified && <span className="verified-badge">✓</span>}
                </div>
                <span className="username">@{account.username}</span>
              </div>
            </Link>
          ))}
        </div>
        <button
          className="see-more-btn"
          onClick={() => setShowMore(!showMore)}
        >
          {showMore ? 'See less' : 'See more'}
        </button>
      </div>

      <div className="sidebar-section">
        <h3>Following accounts</h3>
        <div className="account-list">
          {followingAccounts.map(account => (
            <Link
              key={account.id}
              to={`/profile/${account.username}`}
              className="account-item"
            >
              <img src={account.avatar} alt={account.username} />
              <div className="account-info">
                <div className="account-name-container">
                  <span className="name">{account.name}</span>
                  {account.isVerified && <span className="verified-badge">✓</span>}
                </div>
                <span className="username">@{account.username}</span>
              </div>
            </Link>
          ))}
        </div>
      </div>

      <div className="sidebar-section">
        <h3>Trending Hashtags</h3>
        <div className="hashtag-list">
          {trendingHashtags.map(hashtag => (
            <Link
              key={hashtag.tag}
              to={`/explore/tag/${hashtag.tag}`}
              className="hashtag-item"
            >
              <div className="hashtag-icon">
                <FiTrendingUp />
              </div>
              <div className="hashtag-info">
                <span className="hashtag-name">#{hashtag.tag}</span>
                <span className="hashtag-count">{hashtag.count} views</span>
              </div>
            </Link>
          ))}
        </div>
      </div>

      <div className="sidebar-footer">
        <p>© 2023 VibeVid</p>
        <div className="footer-links">
          <a href="#">About</a>
          <a href="#">Terms</a>
          <a href="#">Privacy</a>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
