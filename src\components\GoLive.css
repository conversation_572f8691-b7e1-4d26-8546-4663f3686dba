.go-live-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px);
}

.go-live-container {
  width: 90%;
  max-width: 1200px;
  height: 90%;
  max-height: 800px;
  background-color: var(--white);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dark .go-live-container {
  background-color: #121212;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.close-live-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  z-index: 10;
  transition: all 0.3s ease;
}

.close-live-btn:hover {
  background-color: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

/* Setup Container */
.setup-container {
  padding: 30px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.setup-container h2 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--text-color);
}

.dark .setup-container h2 {
  color: var(--white);
}

.preview-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto 30px;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  aspect-ratio: 16/9;
}

.dark .preview-container {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.preview-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-color: #000;
}

.camera-off {
  opacity: 0.5;
}

.camera-off-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  gap: 10px;
}

.camera-off-overlay svg {
  font-size: 3rem;
}

.preview-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
}

.control-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

.control-btn.off {
  background-color: #FF3366;
}

.control-btn.settings {
  background-color: rgba(108, 19, 179, 0.8);
}

.control-btn.share {
  background-color: rgba(0, 204, 255, 0.8);
}

/* Stream Form */
.stream-form {
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.form-group label {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-color);
}

.dark .form-group label {
  color: var(--white);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px 15px;
  border-radius: 8px;
  border: 1px solid var(--light-gray);
  font-size: 1rem;
  background-color: var(--white);
  color: var(--text-color);
  transition: all 0.2s;
}

.dark .form-group input,
.dark .form-group select,
.dark .form-group textarea {
  background-color: #1e1e1e;
  border-color: #2a2a2a;
  color: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #6C13B3;
  box-shadow: 0 0 0 2px rgba(108, 19, 179, 0.2);
}

.dark .form-group input:focus,
.dark .form-group select:focus,
.dark .form-group textarea:focus {
  border-color: #9B4BDE;
  box-shadow: 0 0 0 2px rgba(155, 75, 222, 0.3);
}

.char-count {
  position: absolute;
  right: 10px;
  bottom: 10px;
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .char-count {
  color: #aaa;
}

/* Stream Settings */
.stream-settings {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--gray);
  border-radius: 12px;
}

.dark .stream-settings {
  background-color: #1e1e1e;
}

.stream-settings h3 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--text-color);
}

.dark .stream-settings h3 {
  color: var(--white);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.setting-item label {
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--text-color);
}

.dark .setting-item label {
  color: var(--white);
}

.setting-item select {
  width: 150px;
  padding: 8px 10px;
  border-radius: 6px;
  border: 1px solid var(--light-gray);
  background-color: var(--white);
  color: var(--text-color);
}

.dark .setting-item select {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.setting-item.checkbox {
  justify-content: flex-start;
  gap: 10px;
}

.setting-item.checkbox input {
  width: 18px;
  height: 18px;
}

.start-stream-btn {
  margin-top: 30px;
  padding: 15px;
  border-radius: 8px;
  background-color: #FF3366;
  color: white;
  border: none;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.start-stream-btn:hover {
  background-color: #e62e5c;
  transform: translateY(-2px);
}

.start-stream-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.dark .start-stream-btn:disabled {
  background-color: #444;
}

/* Live Stream Container */
.live-stream-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background-color: #FF3366;
  color: white;
  font-weight: 700;
}

.live-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(255, 255, 255, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

.viewer-count {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-left: auto;
}

.live-content {
  flex: 1;
  display: flex;
  height: calc(100% - 50px);
}

.stream-view {
  flex: 1;
  position: relative;
  background-color: #000;
}

.stream-view video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.stream-info-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 10px 15px;
  border-radius: 8px;
  color: white;
}

.stream-info-overlay h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.stream-category {
  font-size: 0.9rem;
  opacity: 0.8;
}

.stream-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 15px;
  align-items: center;
}

.end-stream-btn {
  padding: 10px 20px;
  border-radius: 30px;
  background-color: #FF3366;
  color: white;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.end-stream-btn:hover {
  background-color: #e62e5c;
  transform: scale(1.05);
}

/* Chat Container */
.chat-container {
  width: 300px;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--light-gray);
  background-color: var(--white);
}

.dark .chat-container {
  border-left-color: #2a2a2a;
  background-color: #1e1e1e;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .chat-header {
  border-bottom-color: #2a2a2a;
}

.chat-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
}

.dark .chat-header h3 {
  color: var(--white);
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-message {
  display: flex;
  gap: 10px;
}

.chat-message img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  flex: 1;
  background-color: var(--gray);
  padding: 10px;
  border-radius: 8px;
}

.dark .message-content {
  background-color: #2a2a2a;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.sender-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text-color);
}

.dark .sender-name {
  color: var(--white);
}

.message-time {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .message-time {
  color: #aaa;
}

.message-content p {
  font-size: 0.95rem;
  color: var(--text-color);
  word-break: break-word;
}

.dark .message-content p {
  color: var(--white);
}

.system-message .message-content {
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .system-message .message-content {
  background-color: rgba(155, 75, 222, 0.2);
}

.system-message .sender-name {
  color: #6C13B3;
}

.dark .system-message .sender-name {
  color: #9B4BDE;
}

.empty-chat {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--dark-gray);
  text-align: center;
  padding: 20px;
}

.dark .empty-chat {
  color: #aaa;
}

.chat-input {
  display: flex;
  padding: 15px;
  border-top: 1px solid var(--light-gray);
}

.dark .chat-input {
  border-top-color: #2a2a2a;
}

.chat-input input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid var(--light-gray);
  border-radius: 20px;
  font-size: 0.95rem;
  background-color: var(--gray);
}

.dark .chat-input input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.chat-input input:focus {
  outline: none;
  border-color: #6C13B3;
}

.dark .chat-input input:focus {
  border-color: #9B4BDE;
}

.chat-input button {
  margin-left: 10px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #6C13B3;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-input button:hover {
  background-color: #5a0e9c;
}

.dark .chat-input button {
  background-color: #9B4BDE;
}

.dark .chat-input button:hover {
  background-color: #8a3dcf;
}

.chat-input button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.dark .chat-input button:disabled {
  background-color: #444;
}

/* For mobile devices */
@media (max-width: 768px) {
  .go-live-container {
    width: 100%;
    height: 100%;
    max-height: none;
    border-radius: 0;
  }
  
  .setup-container {
    padding: 20px;
  }
  
  .preview-controls {
    bottom: 10px;
  }
  
  .control-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .live-content {
    flex-direction: column;
  }
  
  .stream-view {
    height: 50%;
  }
  
  .chat-container {
    width: 100%;
    height: 50%;
    border-left: none;
    border-top: 1px solid var(--light-gray);
  }
  
  .dark .chat-container {
    border-top-color: #2a2a2a;
  }
  
  .stream-controls {
    bottom: 10px;
  }
}
