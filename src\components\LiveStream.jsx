import React, { useState, useEffect, useRef } from 'react';
import { Fi<PERSON>sers, FiHeart, FiMessageSquare, FiSend, FiGift, FiShare2, FiX, FiMaximize } from 'react-icons/fi';
import './LiveStream.css';

const LiveStream = ({ 
  stream, 
  onClose,
  currentUser = { username: 'guest_user', avatar: 'https://placehold.co/100' }
}) => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [viewers, setViewers] = useState(stream.viewers || 0);
  const [likes, setLikes] = useState(stream.likes || 0);
  const [isLiked, setIsLiked] = useState(false);
  const [showGiftMenu, setShowGiftMenu] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  const messagesRef = useRef(null);
  const videoRef = useRef(null);
  const containerRef = useRef(null);
  
  // Mock gifts
  const gifts = [
    { id: 1, name: 'Heart', icon: '❤️', value: 5 },
    { id: 2, name: 'Star', icon: '⭐', value: 10 },
    { id: 3, name: 'Trophy', icon: '🏆', value: 50 },
    { id: 4, name: 'Diamond', icon: '💎', value: 100 },
    { id: 5, name: 'Crown', icon: '👑', value: 200 },
    { id: 6, name: 'Rocket', icon: '🚀', value: 500 }
  ];
  
  // Generate mock messages
  useEffect(() => {
    // Initial messages
    const initialMessages = [
      { id: 1, username: 'sarah_j', text: 'Hey everyone! Just joined 👋', avatar: 'https://randomuser.me/api/portraits/women/32.jpg', timestamp: Date.now() - 120000 },
      { id: 2, username: 'mike_t', text: 'This stream is awesome!', avatar: 'https://randomuser.me/api/portraits/men/45.jpg', timestamp: Date.now() - 90000 },
      { id: 3, username: 'lisa_m', text: 'Where are you streaming from?', avatar: 'https://randomuser.me/api/portraits/women/65.jpg', timestamp: Date.now() - 60000 },
      { id: 4, username: stream.username, text: 'Thanks for joining everyone! I\'m streaming from Los Angeles today.', avatar: stream.userAvatar, timestamp: Date.now() - 30000 }
    ];
    
    setMessages(initialMessages);
    
    // Simulate new messages coming in
    const messageInterval = setInterval(() => {
      const randomUsers = [
        { username: 'alex_k', avatar: 'https://randomuser.me/api/portraits/men/22.jpg' },
        { username: 'emma_w', avatar: 'https://randomuser.me/api/portraits/women/44.jpg' },
        { username: 'james_b', avatar: 'https://randomuser.me/api/portraits/men/33.jpg' },
        { username: 'olivia_s', avatar: 'https://randomuser.me/api/portraits/women/57.jpg' }
      ];
      
      const randomTexts = [
        'Love this content! 😍',
        'Where did you get that shirt?',
        'Can you do a tutorial on this?',
        'Greetings from Germany! 🇩🇪',
        'First time catching your stream live!',
        'The lighting is perfect today',
        'Can you play some music?',
        'What camera are you using?',
        'This is so relaxing to watch',
        'You\'re so talented!'
      ];
      
      const randomUser = randomUsers[Math.floor(Math.random() * randomUsers.length)];
      const randomText = randomTexts[Math.floor(Math.random() * randomTexts.length)];
      
      const newMessage = {
        id: Date.now(),
        username: randomUser.username,
        avatar: randomUser.avatar,
        text: randomText,
        timestamp: Date.now()
      };
      
      setMessages(prevMessages => [...prevMessages, newMessage]);
      
      // Randomly increase viewers
      if (Math.random() > 0.7) {
        setViewers(prev => prev + Math.floor(Math.random() * 5) + 1);
      }
      
      // Randomly increase likes
      if (Math.random() > 0.8) {
        setLikes(prev => prev + Math.floor(Math.random() * 10) + 1);
      }
    }, 8000);
    
    return () => clearInterval(messageInterval);
  }, [stream.username, stream.userAvatar]);
  
  // Scroll to bottom of messages when new ones arrive
  useEffect(() => {
    if (messagesRef.current) {
      messagesRef.current.scrollTop = messagesRef.current.scrollHeight;
    }
  }, [messages]);
  
  const handleSendMessage = (e) => {
    e.preventDefault();
    
    if (!message.trim()) return;
    
    const newMessage = {
      id: Date.now(),
      username: currentUser.username,
      avatar: currentUser.avatar,
      text: message,
      timestamp: Date.now()
    };
    
    setMessages(prevMessages => [...prevMessages, newMessage]);
    setMessage('');
  };
  
  const handleLike = () => {
    if (!isLiked) {
      setLikes(likes + 1);
      setIsLiked(true);
      
      // Add heart animation
      const heartContainer = document.querySelector('.live-stream-container');
      if (heartContainer) {
        const heart = document.createElement('div');
        heart.classList.add('floating-heart');
        heart.innerHTML = '❤️';
        heart.style.left = `${Math.random() * 80 + 10}%`;
        heartContainer.appendChild(heart);
        
        setTimeout(() => {
          heartContainer.removeChild(heart);
        }, 3000);
      }
    }
  };
  
  const handleGiftSend = (gift) => {
    // Add gift message
    const giftMessage = {
      id: Date.now(),
      username: currentUser.username,
      avatar: currentUser.avatar,
      text: `Sent a ${gift.name} ${gift.icon}`,
      isGift: true,
      giftValue: gift.value,
      timestamp: Date.now()
    };
    
    setMessages(prevMessages => [...prevMessages, giftMessage]);
    setShowGiftMenu(false);
    
    // Add gift animation
    const giftContainer = document.querySelector('.live-stream-container');
    if (giftContainer) {
      const giftElement = document.createElement('div');
      giftElement.classList.add('floating-gift');
      giftElement.innerHTML = gift.icon;
      giftElement.style.left = `${Math.random() * 80 + 10}%`;
      giftContainer.appendChild(giftElement);
      
      setTimeout(() => {
        giftContainer.removeChild(giftElement);
      }, 3000);
    }
  };
  
  const toggleFullscreen = () => {
    if (!containerRef.current) return;
    
    if (!isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      } else if (containerRef.current.webkitRequestFullscreen) {
        containerRef.current.webkitRequestFullscreen();
      } else if (containerRef.current.mozRequestFullScreen) {
        containerRef.current.mozRequestFullScreen();
      } else if (containerRef.current.msRequestFullscreen) {
        containerRef.current.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
  };
  
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(
        document.fullscreenElement === containerRef.current ||
        document.webkitFullscreenElement === containerRef.current ||
        document.mozFullScreenElement === containerRef.current ||
        document.msFullscreenElement === containerRef.current
      );
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);
  
  // Format time ago
  const formatTimeAgo = (timestamp) => {
    const now = Date.now();
    const diff = now - timestamp;
    
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return 'just now';
    
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m ago`;
    
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };
  
  return (
    <div className="live-stream-container" ref={containerRef}>
      <div className="live-stream-video-container">
        <video
          ref={videoRef}
          src={stream.videoUrl}
          className="live-stream-video"
          autoPlay
          muted={false}
          playsInline
        />
        
        <div className="live-stream-header">
          <div className="live-badge">LIVE</div>
          
          <div className="stream-info">
            <div className="streamer-info">
              <img src={stream.userAvatar} alt={stream.username} className="streamer-avatar" />
              <div className="streamer-details">
                <h3 className="streamer-name">
                  {stream.name}
                  {stream.isVerified && <span className="verified-badge">✓</span>}
                </h3>
                <p className="stream-title">{stream.title || 'Live Stream'}</p>
              </div>
            </div>
            
            <div className="stream-stats">
              <div className="viewers-count">
                <FiUsers />
                <span>{viewers.toLocaleString()}</span>
              </div>
              
              <div className="likes-count">
                <FiHeart className={isLiked ? 'liked' : ''} />
                <span>{likes.toLocaleString()}</span>
              </div>
            </div>
          </div>
          
          <button className="close-stream-btn" onClick={onClose}>
            <FiX />
          </button>
        </div>
        
        <div className="live-stream-actions">
          <button 
            className={`action-btn like-btn ${isLiked ? 'liked' : ''}`}
            onClick={handleLike}
          >
            <FiHeart />
          </button>
          
          <div className="gift-container">
            <button 
              className="action-btn gift-btn"
              onClick={() => setShowGiftMenu(!showGiftMenu)}
            >
              <FiGift />
            </button>
            
            {showGiftMenu && (
              <div className="gift-menu">
                <h4>Send a Gift</h4>
                <div className="gifts-grid">
                  {gifts.map(gift => (
                    <button 
                      key={gift.id} 
                      className="gift-item"
                      onClick={() => handleGiftSend(gift)}
                    >
                      <div className="gift-icon">{gift.icon}</div>
                      <div className="gift-name">{gift.name}</div>
                      <div className="gift-value">{gift.value} coins</div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div className="share-container">
            <button 
              className="action-btn share-btn"
              onClick={() => setShowShareMenu(!showShareMenu)}
            >
              <FiShare2 />
            </button>
            
            {showShareMenu && (
              <div className="share-menu">
                <h4>Share Stream</h4>
                <div className="share-options">
                  <button className="share-option">
                    <div className="share-icon twitter">𝕏</div>
                    <span>Twitter</span>
                  </button>
                  <button className="share-option">
                    <div className="share-icon facebook">f</div>
                    <span>Facebook</span>
                  </button>
                  <button className="share-option">
                    <div className="share-icon whatsapp">W</div>
                    <span>WhatsApp</span>
                  </button>
                  <button className="share-option">
                    <div className="share-icon copy">📋</div>
                    <span>Copy Link</span>
                  </button>
                </div>
              </div>
            )}
          </div>
          
          <button 
            className="action-btn fullscreen-btn"
            onClick={toggleFullscreen}
          >
            <FiMaximize />
          </button>
        </div>
      </div>
      
      <div className="live-chat-container">
        <div className="chat-header">
          <h3>Live Chat</h3>
          <div className="chat-stats">
            <div className="chat-viewers">
              <FiUsers />
              <span>{viewers.toLocaleString()}</span>
            </div>
            <div className="chat-messages">
              <FiMessageSquare />
              <span>{messages.length}</span>
            </div>
          </div>
        </div>
        
        <div className="chat-messages" ref={messagesRef}>
          {messages.map(msg => (
            <div 
              key={msg.id} 
              className={`chat-message ${msg.isGift ? 'gift-message' : ''} ${msg.username === stream.username ? 'streamer-message' : ''}`}
            >
              <img src={msg.avatar} alt={msg.username} className="message-avatar" />
              <div className="message-content">
                <div className="message-header">
                  <span className="message-username">
                    {msg.username}
                    {msg.username === stream.username && (
                      <span className="streamer-badge">Host</span>
                    )}
                  </span>
                  <span className="message-time">{formatTimeAgo(msg.timestamp)}</span>
                </div>
                <p className="message-text">{msg.text}</p>
                {msg.isGift && (
                  <div className="gift-badge" style={{ backgroundColor: `rgba(${msg.giftValue}, 100, 255, 0.2)` }}>
                    <span className="gift-value">{msg.giftValue}</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        <form className="chat-input-container" onSubmit={handleSendMessage}>
          <input
            type="text"
            placeholder="Type a message..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="chat-input"
          />
          <button type="submit" className="send-btn" disabled={!message.trim()}>
            <FiSend />
          </button>
        </form>
      </div>
    </div>
  );
};

export default LiveStream;
