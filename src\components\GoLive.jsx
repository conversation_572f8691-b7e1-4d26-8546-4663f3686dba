import React, { useState, useRef, useEffect } from 'react';
import { FiX, FiVideo, FiMic, FiMicOff, FiVideoOff, FiSettings, FiUsers, FiMessageSquare, FiSend, FiShare2 } from 'react-icons/fi';
import './GoLive.css';

const GoLive = ({ onClose }) => {
  const [isLive, setIsLive] = useState(false);
  const [title, setTitle] = useState('');
  const [category, setCategory] = useState('');
  const [description, setDescription] = useState('');
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [isMicOn, setIsMicOn] = useState(true);
  const [viewers, setViewers] = useState(0);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [streamSettings, setStreamSettings] = useState({
    quality: 'high',
    visibility: 'public',
    allowComments: true,
    enableChat: true
  });
  
  const videoRef = useRef(null);
  const streamRef = useRef(null);
  const chatContainerRef = useRef(null);
  
  // Categories for live streams
  const categories = [
    { id: 'music', name: 'Music' },
    { id: 'gaming', name: 'Gaming' },
    { id: 'cooking', name: 'Cooking' },
    { id: 'talk', name: 'Talk Shows' },
    { id: 'dance', name: 'Dance' },
    { id: 'fitness', name: 'Fitness' },
    { id: 'art', name: 'Art & Creativity' },
    { id: 'education', name: 'Education' },
    { id: 'travel', name: 'Travel & Outdoors' },
    { id: 'tech', name: 'Technology' }
  ];
  
  // Mock viewer data
  const mockViewers = [
    { id: 1, name: 'Emma Wilson', avatar: 'https://randomuser.me/api/portraits/women/44.jpg' },
    { id: 2, name: 'Michael Brown', avatar: 'https://randomuser.me/api/portraits/men/22.jpg' },
    { id: 3, name: 'Sophia Garcia', avatar: 'https://randomuser.me/api/portraits/women/56.jpg' },
    { id: 4, name: 'Daniel Lee', avatar: 'https://randomuser.me/api/portraits/men/45.jpg' }
  ];
  
  // Initialize camera when component mounts
  useEffect(() => {
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices.getUserMedia({ video: true, audio: true })
        .then(stream => {
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
            streamRef.current = stream;
          }
        })
        .catch(err => {
          console.error("Error accessing media devices:", err);
          alert("Could not access camera or microphone. Please check your permissions.");
        });
    }
    
    return () => {
      // Clean up stream when component unmounts
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);
  
  // Scroll chat to bottom when new messages arrive
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);
  
  // Simulate viewers joining when live
  useEffect(() => {
    if (isLive) {
      const interval = setInterval(() => {
        if (Math.random() > 0.7) {
          setViewers(prev => prev + 1);
          
          // Simulate a new chat message
          if (Math.random() > 0.5) {
            const randomViewer = mockViewers[Math.floor(Math.random() * mockViewers.length)];
            const randomMessages = [
              "Great stream!",
              "Hello from California!",
              "Love your content!",
              "First time watching, this is awesome!",
              "Can you do a tutorial on this?",
              "Followed you! Keep up the good work!",
              "What camera are you using?",
              "The lighting looks great!",
              "How long have you been doing this?",
              "👍👍👍"
            ];
            const randomMessage = randomMessages[Math.floor(Math.random() * randomMessages.length)];
            
            addMessage(randomViewer.name, randomMessage, randomViewer.avatar);
          }
        }
      }, 5000);
      
      return () => clearInterval(interval);
    }
  }, [isLive]);
  
  const toggleCamera = () => {
    if (streamRef.current) {
      streamRef.current.getVideoTracks().forEach(track => {
        track.enabled = !isCameraOn;
      });
      setIsCameraOn(!isCameraOn);
    }
  };
  
  const toggleMic = () => {
    if (streamRef.current) {
      streamRef.current.getAudioTracks().forEach(track => {
        track.enabled = !isMicOn;
      });
      setIsMicOn(!isMicOn);
    }
  };
  
  const startLiveStream = () => {
    if (!title || !category) {
      alert("Please enter a title and select a category for your live stream.");
      return;
    }
    
    setIsLive(true);
    setViewers(0);
    
    // Add welcome message
    addMessage('VibeVid', 'Your live stream has started! Share the link with your friends.', '/logo.png', true);
  };
  
  const endLiveStream = () => {
    setIsLive(false);
    
    // In a real app, we would save the stream recording and analytics
    alert(`Live stream ended! You had ${viewers} viewers.`);
  };
  
  const addMessage = (sender, text, avatar, isSystem = false) => {
    const newMsg = {
      id: Date.now(),
      sender,
      text,
      avatar,
      timestamp: new Date(),
      isSystem
    };
    
    setMessages(prev => [...prev, newMsg]);
  };
  
  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newMessage.trim()) {
      // In a real app, this would send the message to a server
      addMessage('You', newMessage, 'https://randomuser.me/api/portraits/men/32.jpg');
      setNewMessage('');
    }
  };
  
  const shareStream = () => {
    // In a real app, this would generate a shareable link
    const streamLink = `https://vibevid.com/live/${Date.now()}`;
    
    if (navigator.share) {
      navigator.share({
        title: title,
        text: `Join my live stream: ${title}`,
        url: streamLink
      })
      .catch(err => console.log('Error sharing:', err));
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(streamLink)
        .then(() => alert('Stream link copied to clipboard!'))
        .catch(err => console.error('Could not copy text: ', err));
    }
  };
  
  return (
    <div className="go-live-overlay">
      <div className="go-live-container">
        <button className="close-live-btn" onClick={onClose}>
          <FiX />
        </button>
        
        {!isLive ? (
          <div className="setup-container">
            <h2>Start a Live Stream</h2>
            
            <div className="preview-container">
              <video 
                ref={videoRef} 
                autoPlay 
                muted 
                playsInline 
                className={!isCameraOn ? 'camera-off' : ''}
              />
              
              {!isCameraOn && (
                <div className="camera-off-overlay">
                  <FiVideoOff />
                  <span>Camera is off</span>
                </div>
              )}
              
              <div className="preview-controls">
                <button 
                  className={`control-btn ${!isMicOn ? 'off' : ''}`}
                  onClick={toggleMic}
                >
                  {isMicOn ? <FiMic /> : <FiMicOff />}
                </button>
                <button 
                  className={`control-btn ${!isCameraOn ? 'off' : ''}`}
                  onClick={toggleCamera}
                >
                  {isCameraOn ? <FiVideo /> : <FiVideoOff />}
                </button>
                <button className="control-btn settings">
                  <FiSettings />
                </button>
              </div>
            </div>
            
            <div className="stream-form">
              <div className="form-group">
                <label htmlFor="stream-title">Stream Title</label>
                <input
                  type="text"
                  id="stream-title"
                  placeholder="Give your stream a title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  maxLength={60}
                />
                <div className="char-count">{title.length}/60</div>
              </div>
              
              <div className="form-group">
                <label htmlFor="stream-category">Category</label>
                <select
                  id="stream-category"
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                >
                  <option value="">Select a category</option>
                  {categories.map(cat => (
                    <option key={cat.id} value={cat.id}>{cat.name}</option>
                  ))}
                </select>
              </div>
              
              <div className="form-group">
                <label htmlFor="stream-description">Description (optional)</label>
                <textarea
                  id="stream-description"
                  placeholder="Tell viewers about your stream"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  maxLength={200}
                  rows={3}
                />
                <div className="char-count">{description.length}/200</div>
              </div>
              
              <div className="stream-settings">
                <h3>Stream Settings</h3>
                
                <div className="setting-item">
                  <label>Stream Quality</label>
                  <select
                    value={streamSettings.quality}
                    onChange={(e) => setStreamSettings({...streamSettings, quality: e.target.value})}
                  >
                    <option value="low">Low (480p)</option>
                    <option value="medium">Medium (720p)</option>
                    <option value="high">High (1080p)</option>
                  </select>
                </div>
                
                <div className="setting-item">
                  <label>Visibility</label>
                  <select
                    value={streamSettings.visibility}
                    onChange={(e) => setStreamSettings({...streamSettings, visibility: e.target.value})}
                  >
                    <option value="public">Public</option>
                    <option value="followers">Followers Only</option>
                    <option value="private">Private</option>
                  </select>
                </div>
                
                <div className="setting-item checkbox">
                  <label>
                    <input
                      type="checkbox"
                      checked={streamSettings.allowComments}
                      onChange={(e) => setStreamSettings({...streamSettings, allowComments: e.target.checked})}
                    />
                    Allow Comments
                  </label>
                </div>
                
                <div className="setting-item checkbox">
                  <label>
                    <input
                      type="checkbox"
                      checked={streamSettings.enableChat}
                      onChange={(e) => setStreamSettings({...streamSettings, enableChat: e.target.checked})}
                    />
                    Enable Live Chat
                  </label>
                </div>
              </div>
              
              <button 
                className="start-stream-btn"
                onClick={startLiveStream}
                disabled={!title || !category}
              >
                Go Live
              </button>
            </div>
          </div>
        ) : (
          <div className="live-stream-container">
            <div className="live-indicator">
              <span className="live-dot"></span>
              <span>LIVE</span>
              <span className="viewer-count">
                <FiUsers />
                {viewers}
              </span>
            </div>
            
            <div className="live-content">
              <div className="stream-view">
                <video 
                  ref={videoRef} 
                  autoPlay 
                  muted 
                  playsInline 
                  className={!isCameraOn ? 'camera-off' : ''}
                />
                
                {!isCameraOn && (
                  <div className="camera-off-overlay">
                    <FiVideoOff />
                    <span>Camera is off</span>
                  </div>
                )}
                
                <div className="stream-info-overlay">
                  <h3>{title}</h3>
                  <p className="stream-category">{categories.find(cat => cat.id === category)?.name}</p>
                </div>
                
                <div className="stream-controls">
                  <button 
                    className={`control-btn ${!isMicOn ? 'off' : ''}`}
                    onClick={toggleMic}
                  >
                    {isMicOn ? <FiMic /> : <FiMicOff />}
                  </button>
                  <button 
                    className={`control-btn ${!isCameraOn ? 'off' : ''}`}
                    onClick={toggleCamera}
                  >
                    {isCameraOn ? <FiVideo /> : <FiVideoOff />}
                  </button>
                  <button className="control-btn share" onClick={shareStream}>
                    <FiShare2 />
                  </button>
                  <button className="end-stream-btn" onClick={endLiveStream}>
                    End Stream
                  </button>
                </div>
              </div>
              
              <div className="chat-container">
                <div className="chat-header">
                  <h3>
                    <FiMessageSquare />
                    Live Chat
                  </h3>
                  <span className="viewer-count">
                    <FiUsers />
                    {viewers}
                  </span>
                </div>
                
                <div className="chat-messages" ref={chatContainerRef}>
                  {messages.length > 0 ? (
                    messages.map(message => (
                      <div 
                        key={message.id} 
                        className={`chat-message ${message.isSystem ? 'system-message' : ''}`}
                      >
                        <img src={message.avatar} alt={message.sender} />
                        <div className="message-content">
                          <div className="message-header">
                            <span className="sender-name">{message.sender}</span>
                            <span className="message-time">
                              {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </span>
                          </div>
                          <p>{message.text}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="empty-chat">
                      <p>No messages yet. Be the first to say hello!</p>
                    </div>
                  )}
                </div>
                
                <form className="chat-input" onSubmit={handleSendMessage}>
                  <input
                    type="text"
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    disabled={!streamSettings.enableChat}
                  />
                  <button 
                    type="submit" 
                    disabled={!newMessage.trim() || !streamSettings.enableChat}
                  >
                    <FiSend />
                  </button>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GoLive;
