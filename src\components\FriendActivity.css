.friend-activity {
  background-color: var(--white);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.dark .friend-activity {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.activity-header {
  margin-bottom: 20px;
}

.activity-header h3 {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--text-color);
}

.dark .activity-header h3 {
  color: var(--white);
}

.activity-tabs {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding-bottom: 5px;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.activity-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.activity-tab {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  border-radius: 20px;
  background-color: var(--gray);
  border: none;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.dark .activity-tab {
  background-color: #2a2a2a;
  color: var(--white);
}

.activity-tab.active {
  background-color: #6C13B3;
  color: white;
}

.dark .activity-tab.active {
  background-color: #9B4BDE;
}

.activity-tab:hover:not(.active) {
  background-color: var(--light-gray);
}

.dark .activity-tab:hover:not(.active) {
  background-color: #3a3a3a;
}

.activity-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 5px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid var(--light-gray);
  position: relative;
}

.dark .activity-item {
  border-bottom-color: #2a2a2a;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.activity-user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 3px;
}

.activity-username {
  font-weight: 600;
  color: var(--text-color);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 5px;
}

.dark .activity-username {
  color: var(--white);
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.8rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.activity-description {
  color: var(--dark-gray);
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark .activity-description {
  color: #aaa;
}

.activity-target-user {
  margin-left: 5px;
  font-weight: 600;
  color: var(--text-color);
  text-decoration: none;
}

.dark .activity-target-user {
  color: var(--white);
}

.activity-time {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .activity-time {
  color: #aaa;
}

.activity-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.activity-icon.like {
  color: #FF3366;
}

.activity-icon.comment, .activity-icon.reply {
  color: #00CCFF;
}

.activity-icon.share {
  color: #33CC33;
}

.activity-icon.post {
  color: #FF9900;
}

.activity-icon.follow {
  color: #6C13B3;
}

.dark .activity-icon.follow {
  color: #9B4BDE;
}

.empty-activity {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
  color: var(--dark-gray);
  font-size: 0.95rem;
}

.dark .empty-activity {
  color: #aaa;
}

/* Responsive styles */
@media (max-width: 768px) {
  .activity-tabs {
    gap: 8px;
  }
  
  .activity-tab {
    padding: 6px 12px;
    font-size: 0.85rem;
  }
  
  .activity-user-avatar {
    width: 35px;
    height: 35px;
  }
  
  .activity-description {
    font-size: 0.9rem;
  }
}
