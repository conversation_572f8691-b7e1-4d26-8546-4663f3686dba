.live-stream-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  height: 80vh;
  max-height: 800px;
  background-color: var(--white);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  position: relative;
}

.dark .live-stream-container {
  background-color: #1e1e1e;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.live-stream-video-container {
  position: relative;
  background-color: #000;
  overflow: hidden;
}

.live-stream-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.live-stream-header {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 15px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  z-index: 2;
}

.live-badge {
  background-color: #FF3366;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 700;
  margin-right: 10px;
  letter-spacing: 1px;
}

.stream-info {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.streamer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid white;
}

.streamer-details {
  color: white;
}

.streamer-name {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 3px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.8rem;
  background-color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stream-title {
  font-size: 0.9rem;
  opacity: 0.9;
}

.stream-stats {
  display: flex;
  gap: 15px;
  color: white;
}

.viewers-count, .likes-count {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
}

.close-stream-btn {
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.close-stream-btn:hover {
  background-color: rgba(255, 51, 102, 0.8);
}

.live-stream-actions {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 2;
}

.action-btn {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: scale(1.1);
}

.like-btn.liked {
  background-color: rgba(255, 51, 102, 0.8);
}

.gift-container, .share-container {
  position: relative;
}

.gift-menu, .share-menu {
  position: absolute;
  bottom: 0;
  right: 55px;
  width: 280px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  padding: 15px;
  z-index: 3;
}

.dark .gift-menu, .dark .share-menu {
  background-color: #1e1e1e;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4);
}

.gift-menu h4, .share-menu h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text-color);
}

.dark .gift-menu h4, .dark .share-menu h4 {
  color: var(--white);
}

.gifts-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.gift-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 10px;
  border-radius: 8px;
  background-color: var(--gray);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .gift-item {
  background-color: #2a2a2a;
}

.gift-item:hover {
  transform: translateY(-3px);
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .gift-item:hover {
  background-color: rgba(155, 75, 222, 0.2);
}

.gift-icon {
  font-size: 1.5rem;
}

.gift-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-color);
}

.dark .gift-name {
  color: var(--white);
}

.gift-value {
  font-size: 0.7rem;
  color: #6C13B3;
}

.dark .gift-value {
  color: #9B4BDE;
}

.share-options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.share-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 10px;
  border-radius: 8px;
  background-color: var(--gray);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .share-option {
  background-color: #2a2a2a;
}

.share-option:hover {
  transform: translateY(-3px);
}

.share-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
}

.share-icon.twitter {
  background-color: #1DA1F2;
}

.share-icon.facebook {
  background-color: #4267B2;
}

.share-icon.whatsapp {
  background-color: #25D366;
}

.share-icon.copy {
  background-color: #6C13B3;
}

.share-option span {
  font-size: 0.8rem;
  color: var(--text-color);
}

.dark .share-option span {
  color: var(--white);
}

.live-chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  border-left: 1px solid var(--light-gray);
}

.dark .live-chat-container {
  border-left-color: #2a2a2a;
}

.chat-header {
  padding: 15px;
  border-bottom: 1px solid var(--light-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark .chat-header {
  border-bottom-color: #2a2a2a;
}

.chat-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
}

.dark .chat-header h3 {
  color: var(--white);
}

.chat-stats {
  display: flex;
  gap: 15px;
}

.chat-viewers, .chat-messages {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.dark .chat-viewers, .dark .chat-messages {
  color: #aaa;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-message {
  display: flex;
  gap: 10px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
}

.message-username {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 5px;
}

.dark .message-username {
  color: var(--white);
}

.streamer-badge {
  background-color: #6C13B3;
  color: white;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
}

.message-time {
  font-size: 0.7rem;
  color: var(--dark-gray);
}

.dark .message-time {
  color: #aaa;
}

.message-text {
  font-size: 0.9rem;
  color: var(--text-color);
  word-break: break-word;
}

.dark .message-text {
  color: var(--white);
}

.gift-message {
  background-color: rgba(108, 19, 179, 0.05);
  padding: 8px;
  border-radius: 8px;
}

.dark .gift-message {
  background-color: rgba(155, 75, 222, 0.1);
}

.gift-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 3px 8px;
  border-radius: 12px;
  margin-top: 5px;
}

.gift-value {
  font-size: 0.8rem;
  font-weight: 600;
  color: #6C13B3;
}

.dark .gift-value {
  color: #9B4BDE;
}

.streamer-message {
  background-color: rgba(255, 51, 102, 0.05);
  padding: 8px;
  border-radius: 8px;
}

.dark .streamer-message {
  background-color: rgba(255, 51, 102, 0.1);
}

.chat-input-container {
  padding: 15px;
  border-top: 1px solid var(--light-gray);
  display: flex;
  gap: 10px;
}

.dark .chat-input-container {
  border-top-color: #2a2a2a;
}

.chat-input {
  flex: 1;
  padding: 10px 15px;
  border-radius: 20px;
  border: 1px solid var(--light-gray);
  background-color: var(--gray);
  font-size: 0.9rem;
  color: var(--text-color);
}

.dark .chat-input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.chat-input:focus {
  outline: none;
  border-color: #6C13B3;
}

.send-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #6C13B3;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .send-btn {
  background-color: #9B4BDE;
}

.send-btn:hover:not(:disabled) {
  transform: scale(1.1);
}

.send-btn:disabled {
  background-color: var(--light-gray);
  cursor: not-allowed;
}

.dark .send-btn:disabled {
  background-color: #3a3a3a;
}

/* Animations */
.floating-heart, .floating-gift {
  position: absolute;
  bottom: 20%;
  font-size: 2rem;
  animation: float 3s ease-out forwards;
  z-index: 3;
}

@keyframes float {
  0% {
    transform: translateY(0) scale(0.5);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: translateY(-300px) scale(1.5);
    opacity: 0;
  }
}

/* Fullscreen mode */
.live-stream-container:fullscreen {
  display: grid;
  grid-template-columns: 3fr 1fr;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .live-stream-container {
    grid-template-columns: 3fr 2fr;
  }
}

@media (max-width: 768px) {
  .live-stream-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 300px;
    height: auto;
    max-height: none;
  }
  
  .streamer-avatar {
    width: 35px;
    height: 35px;
  }
  
  .streamer-name {
    font-size: 1rem;
  }
  
  .stream-title {
    font-size: 0.8rem;
  }
  
  .action-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .gift-menu, .share-menu {
    width: 250px;
    right: 50px;
  }
}

@media (max-width: 480px) {
  .live-stream-header {
    padding: 10px;
  }
  
  .stream-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .stream-stats {
    margin-left: 50px;
  }
  
  .action-btn {
    width: 35px;
    height: 35px;
  }
  
  .gift-menu, .share-menu {
    width: 220px;
    right: 45px;
  }
  
  .gifts-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
