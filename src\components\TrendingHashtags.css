.trending-hashtags {
  background-color: var(--white);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
}

.dark .trending-hashtags {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.trending-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .trending-header {
  border-bottom-color: #2a2a2a;
}

.trending-icon {
  color: #FF3366;
  font-size: 1.2rem;
}

.trending-header h3 {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--text-color);
}

.dark .trending-header h3 {
  color: var(--white);
}

.hashtags-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trending-tag {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;
  background-color: var(--gray);
  animation: fadeIn 0.5s ease forwards;
  opacity: 0;
  transform: translateY(10px);
}

.dark .trending-tag {
  background-color: #2a2a2a;
}

.trending-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  background-color: color-mix(in srgb, var(--tag-color) 10%, var(--gray));
}

.dark .trending-tag:hover {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  background-color: color-mix(in srgb, var(--tag-color) 10%, #2a2a2a);
}

.tag-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.tag-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tag-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-color);
}

.dark .tag-name {
  color: var(--white);
}

.tag-count {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .tag-count {
  color: #aaa;
}

.see-all-link {
  display: block;
  text-align: center;
  margin-top: 15px;
  padding: 10px;
  border-radius: 8px;
  background-color: var(--gray);
  color: #6C13B3;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.dark .see-all-link {
  background-color: #2a2a2a;
  color: #9B4BDE;
}

.see-all-link:hover {
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .see-all-link:hover {
  background-color: rgba(155, 75, 222, 0.1);
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* For mobile devices */
@media (max-width: 768px) {
  .trending-hashtags {
    padding: 15px;
  }
  
  .trending-tag {
    padding: 8px;
  }
  
  .tag-icon {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }
}
