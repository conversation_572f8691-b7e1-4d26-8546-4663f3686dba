.notifications-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.notifications-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
}

.mark-read-btn {
  padding: 8px 16px;
  border: 1px solid var(--light-gray);
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 600;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.dark .mark-read-btn {
  border-color: #2a2a2a;
  color: var(--white);
}

.mark-read-btn:hover {
  background-color: var(--gray);
}

.dark .mark-read-btn:hover {
  background-color: #2a2a2a;
}

.notifications-tabs {
  display: flex;
  overflow-x: auto;
  gap: 10px;
  margin-bottom: 20px;
  padding-bottom: 10px;
}

.notifications-tabs .tab-btn {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  background-color: var(--gray);
  color: var(--dark-gray);
  white-space: nowrap;
  transition: all 0.2s;
}

.dark .notifications-tabs .tab-btn {
  background-color: #2a2a2a;
  color: #aaa;
}

.notifications-tabs .tab-btn:hover {
  background-color: var(--light-gray);
}

.dark .notifications-tabs .tab-btn:hover {
  background-color: #3a3a3a;
}

.notifications-tabs .tab-btn.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.dark .notifications-tabs .tab-btn.active {
  background-color: #9B4BDE;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.notification-item {
  padding: 15px;
  border-radius: 12px;
  background-color: var(--white);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.2s;
}

.dark .notification-item {
  background-color: #1e1e1e;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.dark .notification-item:hover {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.notification-item.unread {
  border-left: 3px solid var(--primary-color);
  background-color: rgba(108, 19, 179, 0.05);
}

.dark .notification-item.unread {
  border-left: 3px solid #9B4BDE;
  background-color: rgba(155, 75, 222, 0.1);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.notification-icon {
  font-size: 1.2rem;
  padding: 8px;
  border-radius: 50%;
  background-color: var(--gray);
  color: var(--dark-gray);
}

.dark .notification-icon {
  background-color: #2a2a2a;
  color: #aaa;
}

.notification-icon.like {
  background-color: rgba(255, 51, 102, 0.1);
  color: #FF3366;
}

.notification-icon.comment {
  background-color: rgba(0, 204, 255, 0.1);
  color: #00CCFF;
}

.notification-icon.follow {
  background-color: rgba(108, 19, 179, 0.1);
  color: #6C13B3;
}

.dark .notification-icon.follow {
  color: #9B4BDE;
}

.notification-icon.mention {
  background-color: rgba(255, 153, 0, 0.1);
  color: #FF9900;
}

.notification-icon.system {
  background-color: rgba(51, 204, 51, 0.1);
  color: #33CC33;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.notification-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.username {
  font-weight: 600;
  font-size: 0.95rem;
}

.message {
  font-size: 0.95rem;
}

.time {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .time {
  color: #aaa;
}

.notification-video {
  flex-shrink: 0;
}

.video-thumbnail {
  width: 60px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.empty-notifications {
  padding: 40px 0;
  text-align: center;
  color: var(--dark-gray);
}

.dark .empty-notifications {
  color: #aaa;
}

/* For mobile devices */
@media (max-width: 768px) {
  .notifications-container {
    padding: 15px;
  }
  
  .notifications-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .notification-content {
    flex-wrap: wrap;
  }
  
  .notification-video {
    margin-left: 63px; /* Align with the text */
    margin-top: 10px;
  }
  
  .video-thumbnail {
    width: 80px;
    height: 100px;
  }
}
