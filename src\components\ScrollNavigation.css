/* Scrollable Container with Arrows */
.scrollable-container-wrapper {
  position: relative;
  width: 100%;
  margin: 10px 0;
}

.scrollable-container {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding: 10px 5px;
  gap: 10px;
}

.scrollable-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.scroll-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--white);
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 5;
  transition: all 0.3s ease;
  color: var(--text-color);
  font-size: 1.2rem;
}

.dark .scroll-arrow {
  background-color: #2a2a2a;
  color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.scroll-arrow:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-50%) scale(1.1);
}

.scroll-left {
  left: -15px;
}

.scroll-right {
  right: -15px;
}

/* Back to Top Button */
.back-to-top-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease;
  font-size: 1.5rem;
}

.back-to-top-button.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.back-to-top-button:hover {
  background-color: #5a10a0;
  transform: translateY(-5px);
}

.dark .back-to-top-button {
  background-color: #9B4BDE;
}

.dark .back-to-top-button:hover {
  background-color: #8a3dcd;
}

/* Section Navigation */
.section-navigation {
  position: fixed;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 90;
  background-color: var(--white);
  padding: 15px 10px;
  border-radius: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.dark .section-navigation {
  background-color: #1e1e1e;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.section-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--dark-gray);
  font-size: 1.2rem;
  padding: 10px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.dark .section-nav-item {
  color: #aaa;
}

.section-nav-item span {
  font-size: 0.7rem;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  white-space: nowrap;
  position: absolute;
  left: 100%;
  margin-left: 10px;
  background-color: var(--white);
  padding: 5px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

.dark .section-nav-item span {
  background-color: #2a2a2a;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.section-nav-item:hover span {
  opacity: 1;
  transform: translateY(0);
}

.section-nav-item.active {
  color: var(--primary-color);
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .section-nav-item.active {
  color: #9B4BDE;
  background-color: rgba(155, 75, 222, 0.2);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .section-navigation {
    left: 10px;
    padding: 10px 5px;
  }
  
  .section-nav-item {
    font-size: 1rem;
    padding: 8px;
  }
  
  .back-to-top-button {
    bottom: 80px;
    right: 20px;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .section-navigation {
    display: none;
  }
  
  .scroll-arrow {
    width: 35px;
    height: 35px;
  }
  
  .scroll-left {
    left: -10px;
  }
  
  .scroll-right {
    right: -10px;
  }
}
