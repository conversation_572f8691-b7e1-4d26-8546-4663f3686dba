import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>lock, FiTrendingUp, FiUsers, FiVideo, FiPlay, FiRadio, FiHeart, FiMessageSquare, FiMaximize } from 'react-icons/fi';
import VideoCard from '../components/VideoCard';
import VideoPlayer from '../components/VideoPlayer';
import VideoCarousel from '../components/VideoCarousel';
import LiveStream from '../components/LiveStream';
import Comments from '../components/Comments';
import AutoplayToggle from '../components/AutoplayToggle';
import Hashtags from '../components/Hashtags';
import FriendActivity from '../components/FriendActivity';
import './Following.css';

const Following = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [showComments, setShowComments] = useState(false);
  const [activeVideo, setActiveVideo] = useState(null);
  const [isAutoplay, setIsAutoplay] = useState(true);
  const [showLiveStream, setShowLiveStream] = useState(false);
  const [activeLiveStream, setActiveLiveStream] = useState(null);
  const [featuredVideo, setFeaturedVideo] = useState(null);
  const videoRefs = useRef({});
  const filterDropdownRef = useRef(null);

  // Mock live streams
  const liveStreams = [
    {
      id: 'live1',
      username: 'emma_wilson',
      name: 'Emma Wilson',
      userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      isVerified: true,
      title: 'Morning workout routine - Join me!',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-woman-doing-exercises-at-home-3775-large.mp4',
      viewers: 1245,
      likes: 876,
      tags: ['fitness', 'workout', 'morning'],
      timestamp: Date.now()
    },
    {
      id: 'live2',
      username: 'alex_johnson',
      name: 'Alex Johnson',
      userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      isVerified: true,
      title: 'Cooking my favorite pasta recipe',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-cooking-with-a-pan-on-a-stove-2753-large.mp4',
      viewers: 2367,
      likes: 1543,
      tags: ['cooking', 'food', 'recipe'],
      timestamp: Date.now()
    },
    {
      id: 'live3',
      username: 'sophia_garcia',
      name: 'Sophia Garcia',
      userAvatar: 'https://randomuser.me/api/portraits/women/56.jpg',
      isVerified: true,
      title: 'NYC street tour - come explore with me!',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-lights-of-times-square-in-new-york-city-4264-large.mp4',
      viewers: 3421,
      likes: 2198,
      tags: ['travel', 'nyc', 'city'],
      timestamp: Date.now()
    }
  ];

  // Mock followed accounts
  const followedAccounts = [
    { username: 'alex_johnson', name: 'Alex Johnson', avatar: 'https://randomuser.me/api/portraits/men/32.jpg', isVerified: true },
    { username: 'emma_wilson', name: 'Emma Wilson', avatar: 'https://randomuser.me/api/portraits/women/44.jpg', isVerified: true },
    { username: 'michael_brown', name: 'Michael Brown', avatar: 'https://randomuser.me/api/portraits/men/22.jpg', isVerified: false },
    { username: 'sophia_garcia', name: 'Sophia Garcia', avatar: 'https://randomuser.me/api/portraits/women/56.jpg', isVerified: true },
    { username: 'daniel_lee', name: 'Daniel Lee', avatar: 'https://randomuser.me/api/portraits/men/45.jpg', isVerified: false },
    { username: 'jessica_taylor', name: 'Jessica Taylor', avatar: 'https://randomuser.me/api/portraits/women/67.jpg', isVerified: true },
    { username: 'ryan_miller', name: 'Ryan Miller', avatar: 'https://randomuser.me/api/portraits/men/55.jpg', isVerified: false },
  ];

  // Mock videos from followed accounts
  const [followingVideos, setFollowingVideos] = useState([
    {
      id: 1,
      username: 'alex_johnson',
      name: 'Alex Johnson',
      userAvatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      isVerified: true,
      caption: 'Trying out this new recipe! So delicious 😋 #food #cooking',
      tags: ['food', 'cooking'],
      music: 'Cooking Beats - Food Channel',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-cooking-with-a-pan-on-a-stove-2753-large.mp4',
      likes: 2345,
      comments: 178,
      shares: 67,
      saved: 89,
      timestamp: Date.now() - 3600000 * 2 // 2 hours ago
    },
    {
      id: 2,
      username: 'emma_wilson',
      name: 'Emma Wilson',
      userAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      isVerified: true,
      caption: 'Workout of the day! Try this routine 💪 #fitness #workout',
      tags: ['fitness', 'workout'],
      music: 'Workout Mix - Fitness Beats',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-woman-doing-exercises-at-home-3775-large.mp4',
      likes: 7890,
      comments: 567,
      shares: 123,
      saved: 234,
      timestamp: Date.now() - 3600000 * 5 // 5 hours ago
    },
    {
      id: 3,
      username: 'michael_brown',
      name: 'Michael Brown',
      userAvatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      isVerified: false,
      caption: "My new puppy! Isn't he adorable? 🐶 #pets #puppy #cute",
      tags: ['pets', 'puppy', 'cute'],
      music: 'Happy Tunes - Pet Lovers',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-a-small-dog-is-played-with-a-stuffed-toy-4538-large.mp4',
      likes: 12345,
      comments: 876,
      shares: 345,
      saved: 123,
      timestamp: Date.now() - 3600000 * 8 // 8 hours ago
    },
    {
      id: 4,
      username: 'sophia_garcia',
      name: 'Sophia Garcia',
      userAvatar: 'https://randomuser.me/api/portraits/women/56.jpg',
      isVerified: true,
      caption: 'City lights and night vibes 🌃 #city #night #urban',
      tags: ['city', 'night', 'urban'],
      music: 'Urban Beats - City Sounds',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-lights-of-times-square-in-new-york-city-4264-large.mp4',
      likes: 15678,
      comments: 987,
      shares: 432,
      saved: 210,
      timestamp: Date.now() - 86400000 // 1 day ago
    },
    {
      id: 5,
      username: 'daniel_lee',
      name: 'Daniel Lee',
      userAvatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      isVerified: false,
      caption: 'Morning coffee and work setup ☕ #productivity #coffee #morning',
      tags: ['productivity', 'coffee', 'morning'],
      music: 'Morning Jazz - Cafe Vibes',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-top-view-of-a-table-with-a-laptop-and-a-30708-large.mp4',
      likes: 3456,
      comments: 234,
      shares: 56,
      saved: 78,
      timestamp: Date.now() - 86400000 * 2 // 2 days ago
    },
    {
      id: 6,
      username: 'jessica_taylor',
      name: 'Jessica Taylor',
      userAvatar: 'https://randomuser.me/api/portraits/women/67.jpg',
      isVerified: true,
      caption: 'Sunset hike with friends was amazing! 🌄 #hiking #adventure #sunset',
      tags: ['hiking', 'adventure', 'sunset'],
      music: 'Adventure Sounds - Nature Mix',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-silhouette-of-a-woman-walking-on-a-mountain-at-sunset-33417-large.mp4',
      likes: 7823,
      comments: 432,
      shares: 156,
      saved: 234,
      timestamp: Date.now() - 3600000 * 12 // 12 hours ago
    },
    {
      id: 7,
      username: 'ryan_miller',
      name: 'Ryan Miller',
      userAvatar: 'https://randomuser.me/api/portraits/men/55.jpg',
      isVerified: false,
      caption: 'Just finished this painting! What do you think? 🎨 #art #painting #creative',
      tags: ['art', 'painting', 'creative'],
      music: 'Creative Flow - Artistic Beats',
      videoUrl: 'https://assets.mixkit.co/videos/preview/mixkit-top-view-of-a-woman-drawing-a-mandala-43486-large.mp4',
      likes: 4567,
      comments: 345,
      shares: 87,
      saved: 123,
      timestamp: Date.now() - 3600000 * 18 // 18 hours ago
    }
  ]);

  // Handle video visibility and autoplay
  // Set featured video on component mount
  useEffect(() => {
    // Set the first video as featured
    if (followingVideos.length > 0) {
      setFeaturedVideo(followingVideos[0]);
    }
  }, []);

  // Handle live stream
  const handleLiveStreamClick = (stream) => {
    setActiveLiveStream(stream);
    setShowLiveStream(true);
  };

  const handleCloseLiveStream = () => {
    setShowLiveStream(false);
  };

  // Handle featured video
  const handleFeaturedVideoClick = (video) => {
    setActiveVideo(video);
    setShowComments(true);
  };

  useEffect(() => {
    const handleScroll = () => {
      if (!isAutoplay) return;

      const filteredVideos = getFilteredVideos();

      filteredVideos.forEach(video => {
        const videoElement = videoRefs.current[video.id];
        if (!videoElement) return;

        const rect = videoElement.getBoundingClientRect();
        const isVisible =
          rect.top >= 0 &&
          rect.top <= window.innerHeight - (rect.height / 2);

        if (isVisible) {
          videoElement.play();
        } else {
          videoElement.pause();
        }
      });
    };

    window.addEventListener('scroll', handleScroll);
    // Initial check
    setTimeout(handleScroll, 1000);

    return () => window.removeEventListener('scroll', handleScroll);
  }, [activeFilter, isAutoplay, followingVideos]);

  const handleVideoRef = (id, element) => {
    videoRefs.current[id] = element;
  };

  const handleCommentClick = (video) => {
    setActiveVideo(video);
    setShowComments(true);
  };

  const getFilteredVideos = () => {
    if (activeFilter === 'all') {
      return followingVideos;
    } else if (activeFilter === 'recent') {
      // Show videos from the last 24 hours
      const oneDayAgo = Date.now() - 86400000;
      return followingVideos.filter(video => video.timestamp > oneDayAgo);
    } else if (activeFilter === 'trending') {
      // Sort by likes (as a proxy for trending)
      return [...followingVideos].sort((a, b) => b.likes - a.likes);
    } else {
      // Filter by username
      return followingVideos.filter(video => video.username === activeFilter);
    }
  };

  return (
    <div className="following-container">
      <div className="following-header">
        <h1>Following</h1>
        <div className="following-filters">
          <button
            className={`filter-btn btn ${activeFilter === 'all' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setActiveFilter('all')}
          >
            <FiUsers />
            <span>All</span>
          </button>
          <button
            className={`filter-btn btn ${activeFilter === 'recent' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setActiveFilter('recent')}
          >
            <FiClock />
            <span>Recent</span>
          </button>
          <button
            className={`filter-btn btn ${activeFilter === 'trending' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setActiveFilter('trending')}
          >
            <FiTrendingUp />
            <span>Trending</span>
          </button>
          <div className="filter-dropdown">
            <button className="filter-dropdown-btn btn btn-secondary">
              <FiFilter />
              <span>Accounts</span>
            </button>
            <div className="filter-dropdown-content">
              {followedAccounts.map(account => (
                <button
                  key={account.username}
                  className={`account-filter-btn btn btn-sm ${activeFilter === account.username ? 'btn-primary' : 'btn-secondary'}`}
                  onClick={() => setActiveFilter(account.username)}
                >
                  <img src={account.avatar} alt={account.name} />
                  <span>{account.name}</span>
                  {account.isVerified && <span className="verified-badge">✓</span>}
                </button>
              ))}
            </div>
          </div>
        </div>

        <AutoplayToggle
          isEnabled={isAutoplay}
          onChange={setIsAutoplay}
          size="medium"
        />
      </div>

      <div className="following-content">
        {/* Live Streams Section */}
        <div className="live-streams-section">
          <div className="section-header">
            <h2>Live Now</h2>
            <button className="go-live-btn">
              <FiRadio />
              Go Live
            </button>
          </div>

          <div className="live-streams-container">
            {liveStreams.map(stream => (
              <div key={stream.id} className="live-stream-card" onClick={() => handleLiveStreamClick(stream)}>
                <div className="live-thumbnail">
                  <video src={stream.videoUrl} muted playsInline loop autoPlay />
                  <div className="live-overlay">
                    <div className="live-badge">LIVE</div>
                    <div className="live-viewers">
                      <FiUsers />
                      <span>{stream.viewers.toLocaleString()}</span>
                    </div>
                  </div>
                  <div className="play-button">
                    <FiPlay />
                  </div>
                </div>
                <div className="live-info">
                  <div className="streamer-info">
                    <img src={stream.userAvatar} alt={stream.name} className="streamer-avatar" />
                    <div>
                      <h4 className="streamer-name">
                        {stream.name}
                        {stream.isVerified && <span className="verified-badge">✓</span>}
                      </h4>
                      <p className="stream-title">{stream.title}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Featured Video Section */}
        {featuredVideo && (
          <div className="featured-video-section">
            <div className="section-header">
              <h2>Featured Video</h2>
            </div>

            <div className="featured-video-container">
              <VideoPlayer
                src={featuredVideo.videoUrl}
                poster={featuredVideo.thumbnail}
                autoPlay={false}
                loop={true}
                muted={false}
                controls={true}
                allowDownload={false}
                allowPictureInPicture={true}
              />

              <div className="featured-video-info">
                <div className="featured-user-info">
                  <img src={featuredVideo.userAvatar} alt={featuredVideo.name} className="featured-user-avatar" />
                  <div>
                    <h3 className="featured-user-name">
                      {featuredVideo.name}
                      {featuredVideo.isVerified && <span className="verified-badge">✓</span>}
                    </h3>
                    <p className="featured-video-caption">{featuredVideo.caption}</p>
                  </div>
                </div>

                <div className="featured-video-stats">
                  <div className="stat-item">
                    <FiHeart />
                    <span>{featuredVideo.likes.toLocaleString()} likes</span>
                  </div>
                  <div className="stat-item">
                    <FiMessageSquare />
                    <span>{featuredVideo.comments.toLocaleString()} comments</span>
                  </div>
                  <button className="comment-btn" onClick={() => handleFeaturedVideoClick(featuredVideo)}>
                    <FiMessageSquare /> Comment
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Video Carousel Section */}
        <VideoCarousel
          title="Trending From Your Network"
          videos={getFilteredVideos().slice(0, 8)}
          onVideoClick={handleCommentClick}
          showAuthor={true}
          showViews={true}
          showDuration={true}
          size="medium"
          seeAllLink="/explore"
        />

        <div className="following-layout">
          <div className="following-main">
            {getFilteredVideos().length > 0 ? (
              <div className="video-feed">
                {getFilteredVideos().map(video => (
                  <VideoCard
                    key={video.id}
                    video={video}
                    onVideoRef={(element) => handleVideoRef(video.id, element)}
                    onCommentClick={() => handleCommentClick(video)}
                    autoplay={isAutoplay}
                  />
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <h2>No videos found</h2>
                <p>Try changing your filter or follow more accounts</p>
              </div>
            )}
          </div>

          <div className="following-sidebar">
            <FriendActivity followedAccounts={followedAccounts} />

            <div className="suggested-follows">
              <h3>Suggested to Follow</h3>
              <div className="suggested-accounts-list">
                {[
                  { username: 'olivia_martinez', name: 'Olivia Martinez', avatar: 'https://randomuser.me/api/portraits/women/23.jpg', isVerified: true, followers: '1.2M' },
                  { username: 'noah_wilson', name: 'Noah Wilson', avatar: 'https://randomuser.me/api/portraits/men/62.jpg', isVerified: false, followers: '456K' },
                  { username: 'ava_thompson', name: 'Ava Thompson', avatar: 'https://randomuser.me/api/portraits/women/89.jpg', isVerified: true, followers: '2.3M' }
                ].map(account => (
                  <div key={account.username} className="suggested-account-item">
                    <img src={account.avatar} alt={account.name} className="account-avatar" />
                    <div className="account-info">
                      <div className="account-name">
                        <span>{account.name}</span>
                        {account.isVerified && <span className="verified-badge">✓</span>}
                      </div>
                      <span className="account-followers">{account.followers} followers</span>
                    </div>
                    <button className="follow-btn btn btn-primary btn-sm">Follow</button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {showComments && activeVideo && (
        <Comments
          video={activeVideo}
          onClose={() => setShowComments(false)}
        />
      )}

      {showLiveStream && activeLiveStream && (
        <div className="live-stream-modal">
          <LiveStream
            stream={activeLiveStream}
            onClose={handleCloseLiveStream}
          />
        </div>
      )}
    </div>
  );
};

export default Following;
