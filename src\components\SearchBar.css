.search-container {
  position: relative;
  z-index: 100;
}

.search-icon-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.dark .search-icon-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.search-icon-btn:hover {
  background-color: var(--light-gray);
  transform: scale(1.05);
}

.dark .search-icon-btn:hover {
  background-color: #3a3a3a;
}

.search-container.expanded {
  width: 100%;
  max-width: 600px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  position: relative;
}

.search-input-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--dark-gray);
  font-size: 1.1rem;
}

.dark .search-icon {
  color: #aaa;
}

.search-input-container input {
  width: 100%;
  padding: 12px 40px 12px 45px;
  border-radius: 30px;
  border: 1px solid var(--light-gray);
  font-size: 1rem;
  background-color: var(--gray);
  color: var(--text-color);
  transition: all 0.2s;
}

.dark .search-input-container input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.search-input-container input:focus {
  outline: none;
  border-color: #6C13B3;
  box-shadow: 0 0 0 2px rgba(108, 19, 179, 0.2);
}

.dark .search-input-container input:focus {
  border-color: #9B4BDE;
  box-shadow: 0 0 0 2px rgba(155, 75, 222, 0.3);
}

.clear-btn {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: var(--dark-gray);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
}

.dark .clear-btn {
  color: #aaa;
}

.search-btn {
  background-color: #6C13B3;
  color: var(--white);
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.search-btn:hover {
  background-color: #5a0e9c;
}

.dark .search-btn {
  background-color: #9B4BDE;
}

.dark .search-btn:hover {
  background-color: #8a3dcf;
}

.close-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.dark .close-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.close-btn:hover {
  background-color: var(--light-gray);
}

.dark .close-btn:hover {
  background-color: #3a3a3a;
}

.search-results {
  position: absolute;
  top: calc(100% + 10px);
  left: 0;
  width: 100%;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  padding: 15px;
  z-index: 10;
  max-height: 400px;
  overflow-y: auto;
}

.dark .search-results {
  background-color: #1e1e1e;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.results-section {
  margin-bottom: 15px;
}

.results-section h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--dark-gray);
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .results-section h4 {
  color: #aaa;
  border-bottom-color: #2a2a2a;
}

.results-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.results-section li {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.results-section li:hover {
  background-color: var(--gray);
}

.dark .results-section li:hover {
  background-color: #2a2a2a;
}

.result-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6C13B3;
  font-size: 1rem;
}

.dark .result-icon {
  background-color: #2a2a2a;
  color: #9B4BDE;
}

.result-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
}

.result-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.result-info {
  flex: 1;
}

.result-text {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-color);
}

.dark .result-text {
  color: var(--white);
}

.result-subtext {
  font-size: 0.85rem;
  color: var(--dark-gray);
  margin-top: 2px;
}

.dark .result-subtext {
  color: #aaa;
}

/* For mobile devices */
@media (max-width: 768px) {
  .search-container.expanded {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--white);
    z-index: 1000;
    padding: 20px;
    display: flex;
    flex-direction: column;
  }
  
  .dark .search-container.expanded {
    background-color: #121212;
  }
  
  .search-form {
    margin-top: 20px;
  }
  
  .search-results {
    position: static;
    box-shadow: none;
    margin-top: 20px;
    flex: 1;
    max-height: none;
  }
}
