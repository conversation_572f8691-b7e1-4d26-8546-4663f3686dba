import React, { useState } from 'react';
import { FiH<PERSON>t, FiMessageSquare, FiShare2, FiClock, FiUser, FiUsers, FiVideo } from 'react-icons/fi';
import { Link } from 'react-router-dom';
import './FriendActivity.css';

const FriendActivity = ({ followedAccounts }) => {
  const [activeTab, setActiveTab] = useState('all');
  
  // Generate mock activity data
  const generateActivityData = () => {
    const activities = [
      'liked a video',
      'commented on a video',
      'shared a video',
      'posted a new video',
      'started following',
      'replied to a comment'
    ];
    
    const activityIcons = {
      'liked a video': <FiHeart className="activity-icon like" />,
      'commented on a video': <FiMessageSquare className="activity-icon comment" />,
      'shared a video': <FiShare2 className="activity-icon share" />,
      'posted a new video': <FiVideo className="activity-icon post" />,
      'started following': <FiUser className="activity-icon follow" />,
      'replied to a comment': <FiMessageSquare className="activity-icon reply" />
    };
    
    const timeAgo = [
      '2m ago', '5m ago', '10m ago', '15m ago', '30m ago', 
      '1h ago', '2h ago', '3h ago', '5h ago', 
      'Yesterday', '2d ago'
    ];
    
    // Generate random activities for each followed account
    const activityData = [];
    
    followedAccounts.forEach(account => {
      // Generate 1-3 activities per account
      const numActivities = Math.floor(Math.random() * 3) + 1;
      
      for (let i = 0; i < numActivities; i++) {
        const activity = activities[Math.floor(Math.random() * activities.length)];
        const time = timeAgo[Math.floor(Math.random() * timeAgo.length)];
        
        // For activities that involve another user
        let targetUser = null;
        if (activity === 'started following') {
          // Pick a random user that's not the current user
          const otherUsers = followedAccounts.filter(user => user.username !== account.username);
          targetUser = otherUsers[Math.floor(Math.random() * otherUsers.length)];
        }
        
        activityData.push({
          id: `${account.username}-${i}`,
          user: account,
          activity,
          icon: activityIcons[activity],
          timeAgo: time,
          targetUser,
          timestamp: Date.now() - (time.includes('m') ? parseInt(time) * 60000 : 
                                  time.includes('h') ? parseInt(time) * 3600000 : 
                                  time === 'Yesterday' ? ******** : 
                                  parseInt(time) * ********)
        });
      }
    });
    
    // Sort by timestamp (most recent first)
    return activityData.sort((a, b) => b.timestamp - a.timestamp);
  };
  
  const allActivities = generateActivityData();
  
  // Filter activities based on active tab
  const getFilteredActivities = () => {
    if (activeTab === 'all') {
      return allActivities;
    } else if (activeTab === 'recent') {
      // Activities from the last 3 hours
      const threeHoursAgo = Date.now() - 3 * 3600000;
      return allActivities.filter(activity => activity.timestamp > threeHoursAgo);
    } else if (activeTab === 'interactions') {
      // Only likes, comments, and shares
      return allActivities.filter(activity => 
        activity.activity === 'liked a video' || 
        activity.activity === 'commented on a video' || 
        activity.activity === 'shared a video' ||
        activity.activity === 'replied to a comment'
      );
    } else if (activeTab === 'posts') {
      // Only new posts
      return allActivities.filter(activity => activity.activity === 'posted a new video');
    }
    return allActivities;
  };
  
  const filteredActivities = getFilteredActivities();
  
  return (
    <div className="friend-activity">
      <div className="activity-header">
        <h3>Friend Activity</h3>
        <div className="activity-tabs">
          <button 
            className={`activity-tab ${activeTab === 'all' ? 'active' : ''}`}
            onClick={() => setActiveTab('all')}
          >
            <FiUsers />
            <span>All</span>
          </button>
          <button 
            className={`activity-tab ${activeTab === 'recent' ? 'active' : ''}`}
            onClick={() => setActiveTab('recent')}
          >
            <FiClock />
            <span>Recent</span>
          </button>
          <button 
            className={`activity-tab ${activeTab === 'interactions' ? 'active' : ''}`}
            onClick={() => setActiveTab('interactions')}
          >
            <FiHeart />
            <span>Interactions</span>
          </button>
          <button 
            className={`activity-tab ${activeTab === 'posts' ? 'active' : ''}`}
            onClick={() => setActiveTab('posts')}
          >
            <FiVideo />
            <span>New Posts</span>
          </button>
        </div>
      </div>
      
      <div className="activity-list">
        {filteredActivities.length > 0 ? (
          filteredActivities.map(activity => (
            <div key={activity.id} className="activity-item">
              <Link to={`/profile/${activity.user.username}`} className="activity-user-avatar">
                <img src={activity.user.avatar} alt={activity.user.name} />
              </Link>
              <div className="activity-content">
                <div className="activity-text">
                  <Link to={`/profile/${activity.user.username}`} className="activity-username">
                    {activity.user.name}
                    {activity.user.isVerified && <span className="verified-badge">✓</span>}
                  </Link>
                  <span className="activity-description">
                    {activity.activity}
                    {activity.targetUser && (
                      <Link to={`/profile/${activity.targetUser.username}`} className="activity-target-user">
                        {activity.targetUser.name}
                      </Link>
                    )}
                  </span>
                </div>
                <span className="activity-time">{activity.timeAgo}</span>
              </div>
              {activity.icon}
            </div>
          ))
        ) : (
          <div className="empty-activity">
            <p>No activity to show</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default FriendActivity;
