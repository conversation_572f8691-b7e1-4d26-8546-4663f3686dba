.video-carousel {
  margin-bottom: 40px;
}

.carousel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.carousel-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-color);
}

.dark .carousel-title {
  color: var(--white);
}

.see-all-link {
  color: #6C13B3;
  font-weight: 600;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.dark .see-all-link {
  color: #9B4BDE;
}

.see-all-link:hover {
  text-decoration: underline;
}

.carousel-container {
  position: relative;
}

.carousel-items {
  display: flex;
  gap: 15px;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding: 10px 5px;
  cursor: grab;
}

.carousel-items::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.carousel-items:active {
  cursor: grabbing;
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--white);
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 5;
  transition: all 0.3s ease;
  color: var(--text-color);
  font-size: 1.2rem;
}

.dark .carousel-arrow {
  background-color: #2a2a2a;
  color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.carousel-arrow:hover {
  background-color: #6C13B3;
  color: white;
  transform: translateY(-50%) scale(1.1);
}

.left-arrow {
  left: -15px;
}

.right-arrow {
  right: -15px;
}

.carousel-item {
  flex: 0 0 auto;
  cursor: pointer;
  transition: transform 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.carousel-small .carousel-item {
  width: 200px;
}

.carousel-medium .carousel-item {
  width: 280px;
}

.carousel-large .carousel-item {
  width: 350px;
}

.carousel-item:hover {
  transform: translateY(-5px);
}

.carousel-thumbnail-container {
  position: relative;
  width: 100%;
  height: 0;
  background-color: #000;
  border-radius: 12px;
  overflow: hidden;
}

.carousel-thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.carousel-item:hover .carousel-thumbnail {
  transform: scale(1.05);
}

.carousel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.carousel-item:hover .carousel-overlay {
  opacity: 1;
}

.play-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(108, 19, 179, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.carousel-item:hover .play-icon {
  transform: scale(1.1);
}

.video-duration {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 3px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.carousel-item-info {
  padding: 12px 8px;
}

.video-title {
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-color);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.dark .video-title {
  color: var(--white);
}

.video-author {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
}

.author-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.author-name {
  font-size: 0.85rem;
  color: var(--dark-gray);
  display: flex;
  align-items: center;
  gap: 5px;
}

.dark .author-name {
  color: #aaa;
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.7rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.video-stats {
  font-size: 0.8rem;
  color: var(--dark-gray);
  display: flex;
  align-items: center;
}

.dark .video-stats {
  color: #aaa;
}

.dot-separator {
  margin: 0 5px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .carousel-small .carousel-item {
    width: 160px;
  }
  
  .carousel-medium .carousel-item {
    width: 220px;
  }
  
  .carousel-large .carousel-item {
    width: 280px;
  }
  
  .carousel-arrow {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .left-arrow {
    left: -10px;
  }
  
  .right-arrow {
    right: -10px;
  }
  
  .play-icon {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .video-title {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .carousel-title {
    font-size: 1.1rem;
  }
  
  .carousel-small .carousel-item {
    width: 140px;
  }
  
  .carousel-medium .carousel-item {
    width: 180px;
  }
  
  .carousel-large .carousel-item {
    width: 240px;
  }
  
  .carousel-item-info {
    padding: 10px 5px;
  }
  
  .video-title {
    font-size: 0.85rem;
    margin-bottom: 5px;
  }
  
  .author-avatar {
    width: 20px;
    height: 20px;
  }
  
  .author-name {
    font-size: 0.8rem;
  }
}
