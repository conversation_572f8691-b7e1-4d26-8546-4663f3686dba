import React, { useState, useRef, useEffect } from 'react';
import { FiSearch, FiX, FiUser, FiHash, FiMusic, FiVideo } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import './SearchBar.css';

const SearchBar = ({ expanded = false, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isExpanded, setIsExpanded] = useState(expanded);
  const [showResults, setShowResults] = useState(false);
  const searchInputRef = useRef(null);
  const searchContainerRef = useRef(null);
  const navigate = useNavigate();
  
  // Mock search results
  const recentSearches = [
    { type: 'hashtag', text: 'dance', icon: <FiHash /> },
    { type: 'user', text: 'emma_wilson', icon: <FiUser /> },
    { type: 'music', text: 'Summer Hits', icon: <FiMusic /> }
  ];
  
  const searchResults = [
    { type: 'user', text: 'emma_wilson', subtext: '<PERSON>', icon: <FiUser />, avatar: 'https://randomuser.me/api/portraits/women/44.jpg' },
    { type: 'hashtag', text: 'dance', subtext: '1.8B views', icon: <FiHash /> },
    { type: 'video', text: 'Dance Tutorial', subtext: 'by emma_wilson', icon: <FiVideo /> },
    { type: 'user', text: 'dance_pro', subtext: 'Dance Professional', icon: <FiUser />, avatar: 'https://randomuser.me/api/portraits/men/32.jpg' },
    { type: 'music', text: 'Dance Mix 2023', subtext: 'Popular track', icon: <FiMusic /> }
  ];
  
  useEffect(() => {
    if (isExpanded && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isExpanded]);
  
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target)) {
        setShowResults(false);
        if (!expanded && isExpanded) {
          setIsExpanded(false);
          if (onClose) onClose();
        }
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [expanded, isExpanded, onClose]);
  
  const handleSearchFocus = () => {
    setShowResults(true);
  };
  
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    setShowResults(true);
  };
  
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
      setShowResults(false);
      if (!expanded) {
        setIsExpanded(false);
        if (onClose) onClose();
      }
    }
  };
  
  const handleResultClick = (result) => {
    switch (result.type) {
      case 'user':
        navigate(`/profile/${result.text}`);
        break;
      case 'hashtag':
        navigate(`/explore/tag/${result.text}`);
        break;
      case 'music':
        navigate(`/music/${encodeURIComponent(result.text)}`);
        break;
      case 'video':
        navigate(`/search?q=${encodeURIComponent(result.text)}`);
        break;
      default:
        navigate(`/search?q=${encodeURIComponent(result.text)}`);
    }
    
    setShowResults(false);
    if (!expanded) {
      setIsExpanded(false);
      if (onClose) onClose();
    }
  };
  
  const handleExpandClick = () => {
    setIsExpanded(true);
  };
  
  const handleCloseClick = () => {
    setIsExpanded(false);
    setShowResults(false);
    if (onClose) onClose();
  };
  
  return (
    <div 
      className={`search-container ${isExpanded ? 'expanded' : ''}`}
      ref={searchContainerRef}
    >
      {!isExpanded ? (
        <button className="search-icon-btn" onClick={handleExpandClick}>
          <FiSearch />
        </button>
      ) : (
        <form className="search-form" onSubmit={handleSearchSubmit}>
          <div className="search-input-container">
            <FiSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search videos, users, or hashtags"
              value={searchQuery}
              onChange={handleSearchChange}
              onFocus={handleSearchFocus}
              ref={searchInputRef}
            />
            {searchQuery && (
              <button 
                type="button" 
                className="clear-btn"
                onClick={() => setSearchQuery('')}
              >
                <FiX />
              </button>
            )}
          </div>
          
          <button type="submit" className="search-btn">Search</button>
          
          {!expanded && (
            <button 
              type="button" 
              className="close-btn"
              onClick={handleCloseClick}
            >
              <FiX />
            </button>
          )}
          
          {showResults && (
            <div className="search-results">
              {searchQuery === '' && recentSearches.length > 0 && (
                <div className="results-section">
                  <h4>Recent Searches</h4>
                  <ul>
                    {recentSearches.map((result, index) => (
                      <li key={index} onClick={() => handleResultClick(result)}>
                        <div className="result-icon">{result.icon}</div>
                        <div className="result-text">{result.text}</div>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
              
              {searchQuery !== '' && (
                <div className="results-section">
                  <h4>Results</h4>
                  <ul>
                    {searchResults
                      .filter(result => 
                        result.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        (result.subtext && result.subtext.toLowerCase().includes(searchQuery.toLowerCase()))
                      )
                      .map((result, index) => (
                        <li key={index} onClick={() => handleResultClick(result)}>
                          {result.type === 'user' && result.avatar ? (
                            <div className="result-avatar">
                              <img src={result.avatar} alt={result.text} />
                            </div>
                          ) : (
                            <div className="result-icon">{result.icon}</div>
                          )}
                          <div className="result-info">
                            <div className="result-text">{result.text}</div>
                            {result.subtext && <div className="result-subtext">{result.subtext}</div>}
                          </div>
                        </li>
                      ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </form>
      )}
    </div>
  );
};

export default SearchBar;
