import React, { useState } from 'react';
import { FiHeart, FiMessageSquare, FiUser, FiVideo, FiStar, FiClock } from 'react-icons/fi';
import './Notifications.css';

const Notifications = () => {
  const [activeTab, setActiveTab] = useState('all');
  
  // Mock notification data
  const notifications = [
    {
      id: 1,
      type: 'like',
      user: {
        username: 'user1',
        name: '<PERSON>',
        avatar: 'https://placehold.co/50'
      },
      content: 'liked your video',
      video: {
        thumbnail: 'https://placehold.co/100x150/FF3366/FFFFFF?text=Video',
        title: 'Sunset vibes at the beach 🌅'
      },
      time: '2 minutes ago',
      isRead: false
    },
    {
      id: 2,
      type: 'comment',
      user: {
        username: 'user2',
        name: '<PERSON>',
        avatar: 'https://placehold.co/50'
      },
      content: 'commented: "This is amazing! 🔥"',
      video: {
        thumbnail: 'https://placehold.co/100x150/00CCFF/FFFFFF?text=Video',
        title: 'City lights and night drives 🌃'
      },
      time: '1 hour ago',
      isRead: false
    },
    {
      id: 3,
      type: 'follow',
      user: {
        username: 'user3',
        name: '<PERSON>',
        avatar: 'https://placehold.co/50'
      },
      content: 'started following you',
      time: '3 hours ago',
      isRead: true
    },
    {
      id: 4,
      type: 'mention',
      user: {
        username: 'user4',
        name: 'Sophia Garcia',
        avatar: 'https://placehold.co/50'
      },
      content: 'mentioned you in a comment: "@user123 check this out!"',
      video: {
        thumbnail: 'https://placehold.co/100x150/6C13B3/FFFFFF?text=Video',
        title: 'New dance challenge! Try it out 💃'
      },
      time: '5 hours ago',
      isRead: true
    },
    {
      id: 5,
      type: 'like',
      user: {
        username: 'user5',
        name: 'Daniel Lee',
        avatar: 'https://placehold.co/50'
      },
      content: 'liked your comment',
      video: {
        thumbnail: 'https://placehold.co/100x150/FF9900/FFFFFF?text=Video',
        title: 'Food tour in Tokyo 🍣'
      },
      time: '1 day ago',
      isRead: true
    },
    {
      id: 6,
      type: 'system',
      content: 'Your video has reached 1,000 views! 🎉',
      video: {
        thumbnail: 'https://placehold.co/100x150/33CC33/FFFFFF?text=Video',
        title: 'Nature walk in the forest 🌲'
      },
      time: '2 days ago',
      isRead: true
    }
  ];
  
  const getFilteredNotifications = () => {
    if (activeTab === 'all') {
      return notifications;
    }
    return notifications.filter(notification => notification.type === activeTab);
  };
  
  const getNotificationIcon = (type) => {
    switch (type) {
      case 'like':
        return <FiHeart className="notification-icon like" />;
      case 'comment':
        return <FiMessageSquare className="notification-icon comment" />;
      case 'follow':
        return <FiUser className="notification-icon follow" />;
      case 'mention':
        return <FiMessageSquare className="notification-icon mention" />;
      case 'system':
        return <FiStar className="notification-icon system" />;
      default:
        return <FiClock className="notification-icon" />;
    }
  };
  
  return (
    <div className="notifications-container">
      <div className="notifications-header">
        <h1>Notifications</h1>
        <button className="mark-read-btn">Mark all as read</button>
      </div>
      
      <div className="notifications-tabs">
        <button 
          className={`tab-btn ${activeTab === 'all' ? 'active' : ''}`}
          onClick={() => setActiveTab('all')}
        >
          All
        </button>
        <button 
          className={`tab-btn ${activeTab === 'like' ? 'active' : ''}`}
          onClick={() => setActiveTab('like')}
        >
          Likes
        </button>
        <button 
          className={`tab-btn ${activeTab === 'comment' ? 'active' : ''}`}
          onClick={() => setActiveTab('comment')}
        >
          Comments
        </button>
        <button 
          className={`tab-btn ${activeTab === 'follow' ? 'active' : ''}`}
          onClick={() => setActiveTab('follow')}
        >
          Follows
        </button>
        <button 
          className={`tab-btn ${activeTab === 'mention' ? 'active' : ''}`}
          onClick={() => setActiveTab('mention')}
        >
          Mentions
        </button>
        <button 
          className={`tab-btn ${activeTab === 'system' ? 'active' : ''}`}
          onClick={() => setActiveTab('system')}
        >
          System
        </button>
      </div>
      
      <div className="notifications-list">
        {getFilteredNotifications().length > 0 ? (
          getFilteredNotifications().map(notification => (
            <div 
              key={notification.id} 
              className={`notification-item ${!notification.isRead ? 'unread' : ''}`}
            >
              <div className="notification-content">
                {getNotificationIcon(notification.type)}
                
                {notification.type !== 'system' && (
                  <img 
                    src={notification.user.avatar} 
                    alt={notification.user.username} 
                    className="user-avatar" 
                  />
                )}
                
                <div className="notification-text">
                  {notification.type !== 'system' && (
                    <span className="username">@{notification.user.username}</span>
                  )}
                  <span className="message">{notification.content}</span>
                  <span className="time">{notification.time}</span>
                </div>
                
                {notification.video && (
                  <div className="notification-video">
                    <img 
                      src={notification.video.thumbnail} 
                      alt={notification.video.title} 
                      className="video-thumbnail" 
                    />
                  </div>
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="empty-notifications">
            <p>No notifications in this category</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Notifications;
