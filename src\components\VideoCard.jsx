import { useState, useRef, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import {
  FiHeart, FiMessageSquare, FiShare2, FiMusic, FiBookmark, FiMoreHorizontal,
  FiVolume2, FiVolumeX, FiMaximize, FiPause, FiPlay, FiEye, FiClock,
  FiThumbsUp, FiThumbsDown, FiFlag, FiDownload, FiSend, FiCopy, FiBarChart2
} from 'react-icons/fi';
import Hashtags from './Hashtags';
import Tooltip from './Tooltip';
import { ThemeContext } from '../contexts/ThemeContext';
import './VideoCard.css';

const VideoCard = ({ video, onVideoRef, onCommentClick, autoplay = true }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [showOptions, setShowOptions] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [showShareOptions, setShowShareOptions] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [showTooltip, setShowTooltip] = useState('');
  const [viewCount, setViewCount] = useState(video.views || Math.floor(Math.random() * 100000) + 5000);
  const [hoverProgress, setHoverProgress] = useState(0);
  const [isHoveringProgress, setIsHoveringProgress] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [volumeLevel, setVolumeLevel] = useState(1);
  const videoRef = useRef(null);
  const { isDarkMode } = useContext(ThemeContext);
  const videoContainerRef = useRef(null);
  const optionsRef = useRef(null);
  const shareOptionsRef = useRef(null);
  const progressBarRef = useRef(null);

  useEffect(() => {
    // Register video element with parent component
    if (videoRef.current && onVideoRef) {
      onVideoRef(videoRef.current);
    }

    // Handle click outside options menu
    const handleClickOutside = (event) => {
      if (optionsRef.current && !optionsRef.current.contains(event.target)) {
        setShowOptions(false);
      }

      if (shareOptionsRef.current && !shareOptionsRef.current.contains(event.target)) {
        setShowShareOptions(false);
      }
    };

    // Increment view count when video plays for at least 3 seconds
    let viewTimer;
    if (isPlaying) {
      viewTimer = setTimeout(() => {
        setViewCount(prevCount => prevCount + 1);
      }, 3000);
    }

    return () => {
      if (viewTimer) clearTimeout(viewTimer);
    };

    // Initialize autoplay if enabled
    if (autoplay && videoRef.current) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              videoRef.current.play().catch(e => console.log('Autoplay prevented:', e));
              setIsPlaying(true);
            } else {
              videoRef.current.pause();
              setIsPlaying(false);
            }
          });
        },
        { threshold: 0.6 }
      );

      if (videoRef.current) {
        observer.observe(videoRef.current);
      }

      return () => {
        if (videoRef.current) {
          observer.unobserve(videoRef.current);
        }
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onVideoRef, autoplay]);

  const handleVideoPress = () => {
    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleTimeUpdate = () => {
    if (!videoRef.current) return;

    const currentTime = videoRef.current.currentTime;
    const duration = videoRef.current.duration;

    if (duration) {
      setProgress((currentTime / duration) * 100);
      setCurrentTime(currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (!videoRef.current) return;
    setDuration(videoRef.current.duration);
  };

  const handleProgressClick = (e) => {
    if (!videoRef.current) return;

    const progressBar = e.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / rect.width;

    videoRef.current.currentTime = pos * videoRef.current.duration;
  };

  const handleProgressHover = (e) => {
    if (!progressBarRef.current) return;

    const rect = progressBarRef.current.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / rect.width;
    setHoverProgress(pos * 100);
    setIsHoveringProgress(true);

    // Show tooltip with time at position
    if (videoRef.current && videoRef.current.duration) {
      const timeAtPosition = pos * videoRef.current.duration;
      setShowTooltip(formatTime(timeAtPosition));
    }
  };

  const handleProgressLeave = () => {
    setIsHoveringProgress(false);
    setShowTooltip('');
  };

  const handleVolumeChange = (e) => {
    if (!videoRef.current) return;

    const newVolume = parseFloat(e.target.value);
    videoRef.current.volume = newVolume;
    setVolumeLevel(newVolume);

    if (newVolume === 0) {
      videoRef.current.muted = true;
      setIsMuted(true);
    } else if (isMuted) {
      videoRef.current.muted = false;
      setIsMuted(false);
    }
  };

  const handleDislike = () => {
    setIsDisliked(!isDisliked);
    if (isLiked) setIsLiked(false);
  };

  const toggleFullscreen = (e) => {
    e.stopPropagation();

    if (!videoContainerRef.current) return;

    if (!isFullscreen) {
      if (videoContainerRef.current.requestFullscreen) {
        videoContainerRef.current.requestFullscreen();
      } else if (videoContainerRef.current.webkitRequestFullscreen) {
        videoContainerRef.current.webkitRequestFullscreen();
      } else if (videoContainerRef.current.msRequestFullscreen) {
        videoContainerRef.current.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.msFullscreenElement
      );
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
    };
  }, []);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  const handleLike = () => {
    setIsLiked(!isLiked);
  };

  const handleSave = () => {
    setIsSaved(!isSaved);
  };

  const handleShare = (e) => {
    e.stopPropagation();
    setShowShareOptions(!showShareOptions);
  };

  const copyToClipboard = () => {
    const videoUrl = `${window.location.origin}/video/${video.id}`;
    navigator.clipboard.writeText(videoUrl)
      .then(() => {
        setShowTooltip('Link copied!');
        setTimeout(() => setShowTooltip(''), 2000);
      })
      .catch(err => console.error('Failed to copy: ', err));

    setShowShareOptions(false);
  };

  const shareToSocial = (platform) => {
    const videoUrl = `${window.location.origin}/video/${video.id}`;
    let shareUrl = '';

    switch(platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(video.caption)}&url=${encodeURIComponent(videoUrl)}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(videoUrl)}`;
        break;
      case 'whatsapp':
        shareUrl = `https://api.whatsapp.com/send?text=${encodeURIComponent(video.caption + ' ' + videoUrl)}`;
        break;
      default:
        return;
    }

    window.open(shareUrl, '_blank');
    setShowShareOptions(false);
  };

  const toggleMute = (e) => {
    e.stopPropagation();
    const videoElement = videoRef.current;
    if (!videoElement) return;

    videoElement.muted = !videoElement.muted;
    setIsMuted(videoElement.muted);
  };

  return (
    <div className="video-card">
      <div className="video-card-header">
        <Link to={`/profile/${video.username}`} className="video-user">
          <img src={video.userAvatar} alt={video.username} className="user-avatar" />
          <div className="user-info">
            <div className="user-name-container">
              <span className="username">{video.name}</span>
              {video.isVerified && <span className="verified-badge" style={{ backgroundColor: '#6C13B3', color: 'white', width: '18px', height: '18px', borderRadius: '50%', display: 'inline-flex', alignItems: 'center', justifyContent: 'center', fontSize: '12px', marginLeft: '5px' }}>✓</span>}
            </div>
            <span className="handle">@{video.username}</span>
          </div>
        </Link>

        <div className="video-header-actions">
          <button className="follow-btn btn btn-primary btn-sm" style={{ backgroundColor: '#6C13B3', color: 'white', border: 'none', borderRadius: '20px', padding: '8px 16px', fontWeight: '600', boxShadow: '0 4px 10px rgba(108, 19, 179, 0.2)' }}>Follow</button>

          <div className="options-menu" ref={optionsRef}>
            <button
              className="options-btn"
              onClick={() => setShowOptions(!showOptions)}
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                backgroundColor: isDarkMode ? '#2a2a2a' : '#f0f0f0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: 'none',
                boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)',
                color: isDarkMode ? 'white' : '#333',
                marginLeft: '10px'
              }}
            >
              <FiMoreHorizontal size={20} />
            </button>

            {showOptions && (
              <div className="options-dropdown">
                <button className="option-item">Not interested</button>
                <button className="option-item">Report</button>
                <button className="option-item">Block user</button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="video-caption">
        <p>{video.caption}</p>
        {video.tags && video.tags.length > 0 && (
          <Hashtags tags={video.tags} size="medium" color="default" />
        )}
        {video.music && (
          <div className="video-music">
            <FiMusic />
            <span>{video.music}</span>
          </div>
        )}
      </div>

      <div className="video-and-social-container">
        <div className="video-container">
          <div className="video-wrapper" ref={videoContainerRef}>
            <div className="video-player-container" onClick={handleVideoPress}>
              <video
                ref={videoRef}
                src={video.videoUrl}
                className="video-player"
                loop
                playsInline
                muted={isMuted}
                onTimeUpdate={handleTimeUpdate}
                onLoadedMetadata={handleLoadedMetadata}
              />

              {!isPlaying && (
                <div className="video-play-overlay">
                  <div className="play-button">
                    <FiPlay />
                  </div>
                </div>
              )}
            </div>

            <div className="video-controls">
              <div
                className="progress-container"
                onClick={handleProgressClick}
                onMouseMove={handleProgressHover}
                onMouseLeave={handleProgressLeave}
                ref={progressBarRef}
              >
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${progress}%` }}
                  ></div>
                  {isHoveringProgress && (
                    <div
                      className="progress-hover-indicator"
                      style={{ left: `${hoverProgress}%` }}
                    >
                      <div className="progress-hover-tooltip">{showTooltip}</div>
                    </div>
                  )}
                </div>
                <div className="time-display">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(duration)}</span>
                </div>
              </div>

              <div className="control-buttons">
                <button className="control-btn" onClick={handleVideoPress}>
                  {isPlaying ? <FiPause /> : <FiPlay />}
                </button>

                <div className="volume-control">
                  <button
                    className="control-btn"
                    onClick={toggleMute}
                    onMouseEnter={() => setShowVolumeSlider(true)}
                  >
                    {isMuted ? <FiVolumeX /> : <FiVolume2 />}
                  </button>

                  {showVolumeSlider && (
                    <div
                      className="volume-slider-container"
                      onMouseLeave={() => setShowVolumeSlider(false)}
                    >
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.01"
                        value={isMuted ? 0 : volumeLevel}
                        onChange={handleVolumeChange}
                        className="volume-slider"
                      />
                    </div>
                  )}
                </div>

                <div className="video-stats">
                  <div className="stat-item">
                    <FiEye />
                    <span>{viewCount.toLocaleString()}</span>
                  </div>
                </div>

                <button className="control-btn" onClick={toggleFullscreen}>
                  <FiMaximize />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Social Buttons Bar */}
        <div className="social-buttons-container">
          {/* Like Button */}
          <div className="social-button-item">
            <button
              className={`social-btn ${isLiked ? 'liked' : ''}`}
              onClick={handleLike}
            >
              <FiHeart size={24} />
            </button>
            <span className={`social-count ${isLiked ? 'liked' : ''}`}>
              {isLiked ? video.likes + 1 : video.likes}
            </span>
          </div>

          {/* Dislike Button */}
          <div className="social-button-item">
            <button
              className={`social-btn ${isDisliked ? 'disliked' : ''}`}
              onClick={handleDislike}
            >
              <FiThumbsDown size={24} />
            </button>
          </div>

          {/* Comment Button */}
          <div className="social-button-item">
            <button
              className="social-btn"
              onClick={() => onCommentClick && onCommentClick()}
            >
              <FiMessageSquare size={24} />
            </button>
            <span className="social-count">
              {video.comments}
            </span>
          </div>

          {/* Share Button */}
          <div className="social-button-item" ref={shareOptionsRef}>
            <button
              className="social-btn"
              onClick={handleShare}
            >
              <FiShare2 size={24} />
            </button>
            <span className="social-count">
              {video.shares}
            </span>

            {showShareOptions && (
              <div className="share-options-dropdown">
                <h4>Share to</h4>
                <div className="share-options-grid">
                  <button className="share-option" onClick={() => shareToSocial('twitter')}>
                    <div className="share-icon twitter">𝕏</div>
                    <span>Twitter</span>
                  </button>
                  <button className="share-option" onClick={() => shareToSocial('facebook')}>
                    <div className="share-icon facebook">f</div>
                    <span>Facebook</span>
                  </button>
                  <button className="share-option" onClick={() => shareToSocial('whatsapp')}>
                    <div className="share-icon whatsapp">W</div>
                    <span>WhatsApp</span>
                  </button>
                  <button className="share-option" onClick={copyToClipboard}>
                    <div className="share-icon copy">
                      <FiCopy />
                    </div>
                    <span>Copy Link</span>
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Save Button */}
          <div className="social-button-item">
            <button
              className={`social-btn ${isSaved ? 'saved' : ''}`}
              onClick={handleSave}
            >
              <FiBookmark size={24} />
            </button>
            <span className={`social-count ${isSaved ? 'saved' : ''}`}>
              {isSaved && video.saved ? video.saved + 1 : (video.saved || 0)}
            </span>
          </div>

          {/* Download Button */}
          <div className="social-button-item">
            <button
              className="social-btn"
              onClick={() => {
                const a = document.createElement('a');
                a.href = video.videoUrl;
                a.download = `vibevid-${video.id}.mp4`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              }}
            >
              <FiDownload size={24} />
            </button>
          </div>
        </div>

        {showTooltip && (
          <div className="tooltip-message">{showTooltip}</div>
        )}
      </div>
    </div>
  );
};

export default VideoCard;
