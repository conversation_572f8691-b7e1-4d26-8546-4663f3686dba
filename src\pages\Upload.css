.upload-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.upload-container h1 {
  font-size: 1.8rem;
  margin-bottom: 5px;
}

.upload-subtitle {
  color: var(--dark-gray);
  margin-bottom: 30px;
}

.upload-content {
  display: flex;
  gap: 30px;
}

.upload-video-container {
  flex: 1;
  border: 2px dashed var(--light-gray);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background-color: var(--gray);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  color: var(--dark-gray);
}

.upload-placeholder p {
  margin-bottom: 5px;
}

.upload-hint {
  color: var(--dark-gray);
  margin-bottom: 15px;
}

.upload-info {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.select-file-btn {
  margin-top: 20px;
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
}

.video-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.remove-video-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: var(--white);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
}

.video-preview {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
}

.upload-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.form-group label {
  font-weight: 600;
  font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
  padding: 12px;
  border: 1px solid var(--light-gray);
  border-radius: 4px;
  font-size: 0.9rem;
  resize: none;
}

.form-group textarea {
  height: 100px;
}

.char-count {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.discard-btn, .post-btn {
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
}

.discard-btn {
  background-color: var(--gray);
  color: var(--text-color);
}

.post-btn {
  background-color: var(--primary-color);
  color: var(--white);
}

.post-btn:disabled {
  background-color: var(--light-gray);
  cursor: not-allowed;
}

/* For mobile devices */
@media (max-width: 768px) {
  .upload-content {
    flex-direction: column;
  }
  
  .upload-video-container {
    min-height: 300px;
  }
}
