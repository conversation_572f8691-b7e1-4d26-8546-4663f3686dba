import React, { useState, useRef, useEffect } from 'react';
import { FiX, FiSend, FiHeart, FiSmile } from 'react-icons/fi';
import './Comments.css';

const Comments = ({ video, onClose }) => {
  const [commentText, setCommentText] = useState('');
  const [comments, setComments] = useState([
    {
      id: 1,
      username: 'em<PERSON>_w<PERSON><PERSON>',
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      text: 'This is amazing! Love the vibes 🔥',
      time: '2 hours ago',
      likes: 45,
      isLiked: false,
      isVerified: true,
      replies: [
        {
          id: 101,
          username: video.username,
          name: video.name,
          avatar: video.userAvatar,
          text: 'Thank you so much! 😊',
          time: '1 hour ago',
          likes: 12,
          isLiked: false,
          isVerified: video.isVerified
        }
      ]
    },
    {
      id: 2,
      username: 'mi<PERSON><PERSON>_brown',
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      text: 'Where was this filmed? Looks beautiful!',
      time: '5 hours ago',
      likes: 23,
      isLiked: false,
      isVerified: false,
      replies: []
    },
    {
      id: 3,
      username: 'sophia_garcia',
      name: '<PERSON> <PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/56.jpg',
      text: 'The music choice is perfect for this 🎵',
      time: '1 day ago',
      likes: 67,
      isLiked: true,
      isVerified: true,
      replies: []
    },
    {
      id: 4,
      username: 'daniel_lee',
      name: 'Daniel Lee',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      text: 'Can you do a tutorial on how you made this?',
      time: '2 days ago',
      likes: 34,
      isLiked: false,
      isVerified: false,
      replies: [
        {
          id: 102,
          username: video.username,
          name: video.name,
          avatar: video.userAvatar,
          text: 'Sure! I\'ll post one next week 👍',
          time: '1 day ago',
          likes: 8,
          isLiked: false,
          isVerified: video.isVerified
        }
      ]
    }
  ]);

  const [replyingTo, setReplyingTo] = useState(null);
  const commentInputRef = useRef(null);
  const commentsContainerRef = useRef(null);

  useEffect(() => {
    // Prevent body scrolling when comments are open
    document.body.style.overflow = 'hidden';

    // Focus the comment input
    if (commentInputRef.current) {
      commentInputRef.current.focus();
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  const handleCommentSubmit = (e) => {
    e.preventDefault();

    if (!commentText.trim()) return;

    const newComment = {
      id: Date.now(),
      username: 'user123', // Current user
      name: 'Current User',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      text: commentText,
      time: 'Just now',
      likes: 0,
      isLiked: false,
      isVerified: false,
      replies: []
    };

    if (replyingTo) {
      // Add reply to the comment
      const updatedComments = comments.map(comment => {
        if (comment.id === replyingTo.id) {
          return {
            ...comment,
            replies: [
              ...comment.replies,
              {
                id: Date.now(),
                username: 'user123', // Current user
                name: 'Current User',
                avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
                text: commentText,
                time: 'Just now',
                likes: 0,
                isLiked: false,
                isVerified: false
              }
            ]
          };
        }
        return comment;
      });

      setComments(updatedComments);
      setReplyingTo(null);
    } else {
      // Add new comment
      setComments([newComment, ...comments]);
    }

    setCommentText('');

    // Scroll to top of comments after adding a new comment
    if (commentsContainerRef.current) {
      commentsContainerRef.current.scrollTop = 0;
    }
  };

  const handleLikeComment = (commentId, isReply = false, parentId = null) => {
    if (isReply && parentId) {
      // Like a reply
      const updatedComments = comments.map(comment => {
        if (comment.id === parentId) {
          return {
            ...comment,
            replies: comment.replies.map(reply => {
              if (reply.id === commentId) {
                return {
                  ...reply,
                  likes: reply.isLiked ? reply.likes - 1 : reply.likes + 1,
                  isLiked: !reply.isLiked
                };
              }
              return reply;
            })
          };
        }
        return comment;
      });

      setComments(updatedComments);
    } else {
      // Like a comment
      const updatedComments = comments.map(comment => {
        if (comment.id === commentId) {
          return {
            ...comment,
            likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1,
            isLiked: !comment.isLiked
          };
        }
        return comment;
      });

      setComments(updatedComments);
    }
  };

  const handleReply = (comment) => {
    setReplyingTo(comment);
    setCommentText(`@${comment.username} `);

    if (commentInputRef.current) {
      commentInputRef.current.focus();
    }
  };

  const cancelReply = () => {
    setReplyingTo(null);
    setCommentText('');
  };

  return (
    <div className="comments-overlay">
      <div className="comments-container">
        <div className="comments-header">
          <h3>Comments ({comments.length})</h3>
          <button className="close-btn" onClick={onClose}>
            <FiX />
          </button>
        </div>

        <div className="comments-list" ref={commentsContainerRef}>
          {comments.map(comment => (
            <div key={comment.id} className="comment">
              <img src={comment.avatar} alt={comment.name} className="comment-avatar" />

              <div className="comment-content">
                <div className="comment-header">
                  <div className="comment-user">
                    <span className="comment-username">{comment.name}</span>
                    {comment.isVerified && <span className="verified-badge">✓</span>}
                    <span className="comment-handle">@{comment.username}</span>
                  </div>
                  <span className="comment-time">{comment.time}</span>
                </div>

                <p className="comment-text">{comment.text}</p>

                <div className="comment-actions">
                  <button
                    className={`like-btn ${comment.isLiked ? 'liked' : ''}`}
                    onClick={() => handleLikeComment(comment.id)}
                  >
                    <FiHeart />
                    <span>{comment.likes}</span>
                  </button>

                  <button className="reply-btn" onClick={() => handleReply(comment)}>
                    Reply
                  </button>
                </div>

                {comment.replies.length > 0 && (
                  <div className="comment-replies">
                    {comment.replies.map(reply => (
                      <div key={reply.id} className="reply">
                        <img src={reply.avatar} alt={reply.name} className="reply-avatar" />

                        <div className="reply-content">
                          <div className="comment-header">
                            <div className="comment-user">
                              <span className="comment-username">{reply.name}</span>
                              {reply.isVerified && <span className="verified-badge">✓</span>}
                              <span className="comment-handle">@{reply.username}</span>
                            </div>
                            <span className="comment-time">{reply.time}</span>
                          </div>

                          <p className="comment-text">{reply.text}</p>

                          <div className="comment-actions">
                            <button
                              className={`like-btn ${reply.isLiked ? 'liked' : ''}`}
                              onClick={() => handleLikeComment(reply.id, true, comment.id)}
                            >
                              <FiHeart />
                              <span>{reply.likes}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="comment-input-container">
          {replyingTo && (
            <div className="replying-to">
              <span>Replying to @{replyingTo.username}</span>
              <button className="cancel-reply" onClick={cancelReply}>
                <FiX />
              </button>
            </div>
          )}

          <form onSubmit={handleCommentSubmit}>
            <div className="comment-input-wrapper">
              <input
                type="text"
                placeholder="Add a comment..."
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                ref={commentInputRef}
              />

              <div className="comment-input-actions">
                <button type="button" className="emoji-btn">
                  <FiSmile />
                </button>

                <button
                  type="submit"
                  className="post-btn"
                  disabled={!commentText.trim()}
                >
                  <FiSend />
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Comments;
