.messages-container {
  display: flex;
  height: calc(100vh - 60px);
  max-width: 1200px;
  margin: 0 auto;
}

.messages-sidebar {
  width: 350px;
  border-right: 1px solid var(--light-gray);
  display: flex;
  flex-direction: column;
  background-color: var(--white);
}

.dark .messages-sidebar {
  background-color: #1e1e1e;
  border-right-color: #2a2a2a;
}

.messages-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--light-gray);
}

.dark .messages-header {
  border-bottom-color: #2a2a2a;
}

.messages-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
}

.new-message-btn {
  padding: 8px 16px;
  background-color: #6C13B3;
  color: var(--white);
  border: none;
  border-radius: 4px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-message-btn:hover {
  background-color: #5a0e9c;
}

.search-messages {
  padding: 15px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 25px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--dark-gray);
}

.search-messages input {
  width: 100%;
  padding: 10px 10px 10px 35px;
  border: 1px solid var(--light-gray);
  border-radius: 20px;
  font-size: 0.9rem;
  background-color: var(--gray);
}

.dark .search-messages input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  display: flex;
  padding: 15px;
  gap: 15px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid var(--light-gray);
}

.dark .conversation-item {
  border-bottom-color: #2a2a2a;
}

.conversation-item:hover {
  background-color: var(--gray);
}

.dark .conversation-item:hover {
  background-color: #2a2a2a;
}

.conversation-item.active {
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .conversation-item.active {
  background-color: rgba(155, 75, 222, 0.2);
}

.conversation-avatar {
  position: relative;
}

.conversation-avatar img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.online-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background-color: #33CC33;
  border-radius: 50%;
  border: 2px solid var(--white);
}

.dark .online-indicator {
  border-color: #1e1e1e;
}

.conversation-info {
  flex: 1;
  min-width: 0; /* Allows text to truncate */
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.conversation-name {
  font-size: 1rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-time {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .conversation-time {
  color: #aaa;
}

.conversation-preview {
  display: flex;
  align-items: center;
  gap: 5px;
}

.sent-indicator {
  font-weight: 600;
  font-size: 0.9rem;
}

.preview-text {
  font-size: 0.9rem;
  color: var(--dark-gray);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.dark .preview-text {
  color: #aaa;
}

.conversation-item.unread .preview-text {
  color: var(--text-color);
  font-weight: 600;
}

.dark .conversation-item.unread .preview-text {
  color: var(--white);
}

.unread-badge {
  background-color: #6C13B3;
  color: var(--white);
  font-size: 0.7rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.no-conversations {
  padding: 30px;
  text-align: center;
  color: var(--dark-gray);
}

.dark .no-conversations {
  color: #aaa;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--white);
}

.dark .chat-container {
  background-color: #121212;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .chat-header {
  border-bottom-color: #2a2a2a;
}

.chat-user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.chat-username {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 3px;
}

.chat-status {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .chat-status {
  color: #aaa;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.chat-action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray);
  color: var(--text-color);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dark .chat-action-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.chat-action-btn:hover {
  background-color: var(--light-gray);
}

.dark .chat-action-btn:hover {
  background-color: #3a3a3a;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
  background-color: var(--gray);
}

.dark .chat-messages {
  background-color: #1a1a1a;
}

.message {
  display: flex;
  max-width: 70%;
}

.message.received {
  align-self: flex-start;
}

.message.sent {
  align-self: flex-end;
}

.message-content {
  padding: 12px 15px;
  border-radius: 18px;
  position: relative;
}

.message.received .message-content {
  background-color: var(--white);
  border-bottom-left-radius: 4px;
}

.dark .message.received .message-content {
  background-color: #2a2a2a;
}

.message.sent .message-content {
  background-color: #6C13B3;
  color: var(--white);
  border-bottom-right-radius: 4px;
}

.message-content p {
  margin-bottom: 5px;
  font-size: 0.95rem;
  line-height: 1.4;
}

.message-time {
  font-size: 0.7rem;
  color: var(--dark-gray);
  display: block;
  text-align: right;
}

.message.sent .message-time {
  color: rgba(255, 255, 255, 0.7);
}

.message-input-container {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  border-top: 1px solid var(--light-gray);
}

.dark .message-input-container {
  border-top-color: #2a2a2a;
}

.message-attachments {
  display: flex;
  gap: 10px;
}

.attachment-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray);
  color: var(--text-color);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dark .attachment-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.attachment-btn:hover {
  background-color: var(--light-gray);
}

.dark .attachment-btn:hover {
  background-color: #3a3a3a;
}

.message-input-container input {
  flex: 1;
  padding: 12px 15px;
  border: 1px solid var(--light-gray);
  border-radius: 20px;
  font-size: 0.95rem;
  background-color: var(--gray);
}

.dark .message-input-container input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.send-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #6C13B3;
  color: var(--white);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-btn:hover {
  background-color: #5a0e9c;
}

.send-btn:disabled {
  background-color: var(--light-gray);
  cursor: not-allowed;
}

.dark .send-btn:disabled {
  background-color: #3a3a3a;
}

.empty-chat {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray);
}

.dark .empty-chat {
  background-color: #1a1a1a;
}

.empty-chat-content {
  text-align: center;
  padding: 30px;
}

.empty-chat-content h3 {
  font-size: 1.3rem;
  margin-bottom: 10px;
}

.empty-chat-content p {
  color: var(--dark-gray);
  margin-bottom: 20px;
}

.dark .empty-chat-content p {
  color: #aaa;
}

/* For mobile devices */
@media (max-width: 768px) {
  .messages-container {
    flex-direction: column;
  }
  
  .messages-sidebar {
    width: 100%;
    height: 100%;
    display: none; /* Hide by default on mobile */
  }
  
  .messages-sidebar.active {
    display: flex; /* Show when active */
  }
  
  .chat-container {
    display: flex; /* Show by default on mobile */
  }
  
  .chat-container.hidden {
    display: none; /* Hide when viewing conversations list */
  }
  
  .chat-header {
    padding: 10px 15px;
  }
  
  .message {
    max-width: 85%;
  }
  
  .message-input-container {
    padding: 10px 15px;
  }
}
