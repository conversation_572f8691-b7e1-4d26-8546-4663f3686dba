.settings-container {
  display: flex;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  gap: 30px;
}

.settings-sidebar {
  width: 250px;
  flex-shrink: 0;
}

.settings-sidebar h2 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--light-gray);
}

.settings-tabs {
  list-style: none;
  padding: 0;
}

.settings-tab {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  margin-bottom: 5px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.settings-tab:hover {
  background-color: var(--gray);
}

.settings-tab.active {
  background-color: rgba(108, 19, 179, 0.1);
  color: #6C13B3;
  font-weight: 600;
}

.dark .settings-tab.active {
  background-color: rgba(155, 75, 222, 0.2);
  color: #9B4BDE;
}

.settings-content {
  flex: 1;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 30px;
}

.dark .settings-content {
  background-color: #1e1e1e;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.settings-section h2 {
  font-size: 1.5rem;
  margin-bottom: 25px;
  color: var(--text-color);
}

.dark .settings-section h2 {
  color: var(--white);
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 12px;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  font-size: 0.95rem;
  background-color: var(--white);
}

.dark .form-group input,
.dark .form-group select,
.dark .form-group textarea {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.setting-description {
  font-size: 0.85rem;
  color: var(--dark-gray);
  margin-top: 2px;
}

.dark .setting-description {
  color: #aaa;
}

.switch-group {
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.switch-group > div {
  flex: 1;
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #6C13B3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #6C13B3;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.save-btn {
  background-color: #6C13B3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: background-color 0.2s;
}

.save-btn:hover {
  background-color: #5a0e9c;
}

.secondary-btn {
  background-color: transparent;
  color: #6C13B3;
  border: 1px solid #6C13B3;
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.secondary-btn:hover {
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .secondary-btn {
  color: #9B4BDE;
  border-color: #9B4BDE;
}

.dark .secondary-btn:hover {
  background-color: rgba(155, 75, 222, 0.2);
}

/* Help Section */
.help-section {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.help-card {
  background-color: var(--gray);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 10px;
}

.dark .help-card {
  background-color: #2a2a2a;
}

.help-card h3 {
  margin: 10px 0 5px;
  font-size: 1.1rem;
}

.help-card p {
  color: var(--dark-gray);
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.dark .help-card p {
  color: #aaa;
}

.app-info {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--light-gray);
  text-align: center;
}

.dark .app-info {
  border-top-color: #2a2a2a;
}

.app-info h3 {
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.app-info p {
  color: var(--dark-gray);
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.dark .app-info p {
  color: #aaa;
}

.links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 15px;
}

.links a {
  color: #6C13B3;
  text-decoration: none;
  font-size: 0.9rem;
}

.links a:hover {
  text-decoration: underline;
}

.dark .links a {
  color: #9B4BDE;
}

/* Security Settings */
.password-field {
  display: flex;
  gap: 10px;
}

.password-field input {
  flex: 1;
}

.login-activity {
  background-color: var(--gray);
  border-radius: 8px;
  padding: 15px;
}

.dark .login-activity {
  background-color: #2a2a2a;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid var(--light-gray);
}

.dark .activity-item {
  border-bottom-color: #3a3a3a;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item > div {
  display: flex;
  align-items: center;
  gap: 15px;
}

.device-name {
  font-weight: 600;
  margin-bottom: 3px;
}

.activity-time {
  font-size: 0.85rem;
  color: var(--dark-gray);
}

.dark .activity-time {
  color: #aaa;
}

.current-device {
  background-color: rgba(108, 19, 179, 0.1);
  color: #6C13B3;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.dark .current-device {
  background-color: rgba(155, 75, 222, 0.2);
  color: #9B4BDE;
}

.logout-btn {
  background-color: transparent;
  color: #FF3366;
  border: 1px solid #FF3366;
  border-radius: 8px;
  padding: 5px 10px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
}

.logout-btn:hover {
  background-color: rgba(255, 51, 102, 0.1);
}

.danger-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  color: #FF3366;
  border: 1px solid #FF3366;
  border-radius: 8px;
  padding: 10px 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.danger-btn:hover {
  background-color: rgba(255, 51, 102, 0.1);
}

/* Data & Storage Settings */
.storage-usage {
  background-color: var(--gray);
  border-radius: 8px;
  padding: 20px;
  margin-top: 10px;
}

.dark .storage-usage {
  background-color: #2a2a2a;
}

.storage-usage h3 {
  font-size: 1.1rem;
  margin-bottom: 15px;
}

.storage-bar {
  height: 10px;
  background-color: var(--light-gray);
  border-radius: 5px;
  margin-bottom: 10px;
  overflow: hidden;
}

.dark .storage-bar {
  background-color: #3a3a3a;
}

.storage-fill {
  height: 100%;
  background: linear-gradient(to right, #6C13B3, #FF3366);
  border-radius: 5px;
}

.storage-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Payment Settings */
.payment-methods {
  margin-bottom: 30px;
}

.payment-methods h3,
.billing-history h3,
.subscription h3 {
  font-size: 1.1rem;
  margin-bottom: 15px;
}

.payment-method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--gray);
  border-radius: 8px;
  margin-bottom: 15px;
}

.dark .payment-method-item {
  background-color: #2a2a2a;
}

.payment-method-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.payment-icon {
  width: 40px;
  height: 25px;
  border-radius: 4px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-icon.visa {
  background-color: #1A1F71;
  position: relative;
}

.payment-icon.visa::after {
  content: 'VISA';
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
}

.payment-name {
  font-weight: 600;
  margin-bottom: 3px;
}

.payment-expiry {
  font-size: 0.85rem;
  color: var(--dark-gray);
}

.dark .payment-expiry {
  color: #aaa;
}

.payment-actions {
  display: flex;
  gap: 15px;
}

.text-btn {
  background: none;
  border: none;
  color: #6C13B3;
  font-weight: 600;
  cursor: pointer;
  padding: 5px;
}

.dark .text-btn {
  color: #9B4BDE;
}

.text-btn:hover {
  text-decoration: underline;
}

.add-payment {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 10px;
}

.billing-history {
  margin-bottom: 30px;
}

.billing-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid var(--light-gray);
}

.dark .billing-item {
  border-bottom-color: #3a3a3a;
}

.billing-date {
  font-weight: 600;
  margin-bottom: 3px;
}

.billing-description {
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.dark .billing-description {
  color: #aaa;
}

.billing-amount {
  font-weight: 600;
}

.view-all {
  margin-top: 15px;
  display: block;
  text-align: center;
}

.subscription-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--gray);
  border-radius: 8px;
}

.dark .subscription-info {
  background-color: #2a2a2a;
}

.subscription-name {
  font-weight: 600;
  margin-bottom: 3px;
}

.subscription-price {
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.dark .subscription-price {
  color: #aaa;
}

/* Content Preferences */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
  margin-top: 10px;
}

.category-item {
  background-color: var(--gray);
  border-radius: 8px;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
}

.dark .category-item {
  background-color: #2a2a2a;
}

.category-item.selected {
  background-color: rgba(108, 19, 179, 0.1);
  color: #6C13B3;
  font-weight: 600;
}

.dark .category-item.selected {
  background-color: rgba(155, 75, 222, 0.2);
  color: #9B4BDE;
}

.category-item:hover {
  transform: translateY(-2px);
}

.not-interested-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.tag {
  background-color: var(--gray);
  border-radius: 20px;
  padding: 5px 10px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.dark .tag {
  background-color: #2a2a2a;
}

.remove-tag {
  background: none;
  border: none;
  color: var(--dark-gray);
  cursor: pointer;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  width: 18px;
  height: 18px;
}

.dark .remove-tag {
  color: #aaa;
}

.remove-tag:hover {
  color: #FF3366;
}

/* Responsive */
@media (max-width: 768px) {
  .settings-container {
    flex-direction: column;
  }

  .settings-sidebar {
    width: 100%;
  }

  .settings-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .settings-tab {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .switch-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .switch-group .switch {
    margin-left: auto;
  }
}
