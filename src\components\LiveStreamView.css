.live-stream-view {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--white);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dark .live-stream-view {
  background-color: #121212;
}

.stream-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--white);
  z-index: 10;
}

.dark .stream-header {
  border-bottom-color: #2a2a2a;
  background-color: #121212;
}

.stream-title-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stream-title-info h2 {
  font-size: 1.3rem;
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.dark .stream-title-info h2 {
  color: var(--white);
}

.close-stream-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  color: var(--text-color);
  transition: all 0.3s ease;
}

.dark .close-stream-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.close-stream-btn:hover {
  background-color: var(--light-gray);
  transform: scale(1.1);
}

.dark .close-stream-btn:hover {
  background-color: #3a3a3a;
}

.stream-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.stream-player {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  overflow-y: auto;
}

.player-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

.video-container {
  position: relative;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  background-color: #000;
  aspect-ratio: 16/9;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark .video-container {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.stream-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 15px;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5) 0%, transparent 30%, transparent 70%, rgba(0, 0, 0, 0.5) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-container:hover .video-overlay {
  opacity: 1;
}

.stream-stats {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.stat {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--white);
  font-size: 0.9rem;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 5px 10px;
  border-radius: 4px;
}

.stat.viewers {
  background-color: rgba(255, 51, 102, 0.8);
}

.stream-duration {
  color: var(--white);
  font-size: 0.9rem;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 5px 10px;
  border-radius: 4px;
}

.video-controls {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.video-control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.video-control-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.volume-slider {
  width: 80px;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20px;
  padding: 0 10px;
}

.volume-slider input {
  width: 100%;
  -webkit-appearance: none;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
}

.volume-slider input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: white;
  cursor: pointer;
}

.stream-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--gray);
  border-radius: 12px;
}

.dark .stream-info-bar {
  background-color: #1e1e1e;
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.streamer-info img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.streamer-info h3 {
  font-size: 1.1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
  color: var(--text-color);
}

.dark .streamer-info h3 {
  color: var(--white);
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.8rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.stream-category {
  font-size: 0.9rem;
  color: var(--dark-gray);
  text-transform: capitalize;
}

.dark .stream-category {
  color: #aaa;
}

.stream-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.follow-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dark .action-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.action-btn:hover {
  background-color: var(--light-gray);
}

.dark .action-btn:hover {
  background-color: #3a3a3a;
}

.interaction-bar {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  padding: 15px 20px;
  margin-top: 20px;
  margin-bottom: 10px;
  background-color: var(--white);
  border-radius: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 5;
}

.dark .interaction-bar {
  background-color: #1e1e1e;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);
}

.interaction-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(to right, #6C13B3, #FF3366);
}

.dark .interaction-bar::before {
  background: linear-gradient(to right, #9B4BDE, #FF5C85);
}

.interaction-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-color);
}

.dark .interaction-btn {
  color: var(--white);
}

.interaction-btn span {
  font-size: 0.85rem;
  font-weight: 500;
  color: var(--dark-gray);
}

.dark .interaction-btn span {
  color: #aaa;
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.4rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  border: none;
}

.dark .action-icon {
  background-color: #2a2a2a;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.interaction-btn:hover .action-icon {
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(108, 19, 179, 0.15);
  background-color: #6C13B3;
  color: white;
}

.dark .interaction-btn:hover .action-icon {
  box-shadow: 0 6px 15px rgba(155, 75, 222, 0.2);
  background-color: #9B4BDE;
  color: white;
}

.interaction-btn.liked .action-icon {
  color: white;
  background-color: #FF3366;
  box-shadow: 0 4px 12px rgba(255, 51, 102, 0.3);
}

.interaction-btn.saved .action-icon {
  color: white;
  background-color: #6C13B3;
  box-shadow: 0 4px 12px rgba(108, 19, 179, 0.3);
}

.dark .interaction-btn.saved .action-icon {
  color: white;
  background-color: #9B4BDE;
  box-shadow: 0 4px 12px rgba(155, 75, 222, 0.3);
}

.interaction-btn.gift .action-icon {
  color: #FF3366;
}

.interaction-btn.gift:hover .action-icon {
  background-color: #FF3366;
  color: white;
}

.stream-description {
  padding: 20px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-top: 10px;
}

.dark .stream-description {
  background-color: #1e1e1e;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.stream-description h3 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 10px;
  color: var(--text-color);
}

.dark .stream-description h3 {
  color: var(--white);
}

.stream-description p {
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 15px;
  color: var(--text-color);
}

.dark .stream-description p {
  color: var(--white);
}

.stream-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.stream-tag {
  font-size: 0.9rem;
  color: #6C13B3;
  font-weight: 600;
  transition: all 0.2s ease;
}

.dark .stream-tag {
  color: #9B4BDE;
}

.stream-tag:hover {
  transform: scale(1.05);
  opacity: 0.8;
}

.stream-chat {
  width: 350px;
  display: flex;
  flex-direction: column;
  border-left: 1px solid var(--light-gray);
  background-color: var(--white);
}

.dark .stream-chat {
  background-color: #1e1e1e;
  border-left-color: #2a2a2a;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .chat-header {
  border-bottom-color: #2a2a2a;
}

.chat-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
}

.dark .chat-header h3 {
  color: var(--white);
}

.chat-viewers {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--dark-gray);
  font-size: 0.9rem;
}

.dark .chat-viewers {
  color: #aaa;
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.chat-message {
  display: flex;
  gap: 10px;
}

.chat-message img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.message-content {
  flex: 1;
  background-color: var(--gray);
  padding: 10px;
  border-radius: 8px;
}

.dark .message-content {
  background-color: #2a2a2a;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
}

.message-content h4 {
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.dark .message-content h4 {
  color: var(--white);
}

.message-time {
  font-size: 0.75rem;
  color: var(--dark-gray);
}

.dark .message-time {
  color: #aaa;
}

.message-content p {
  font-size: 0.9rem;
  margin: 0;
  color: var(--text-color);
  word-break: break-word;
}

.dark .message-content p {
  color: var(--white);
}

.chat-message.gift .message-content {
  background-color: rgba(255, 51, 102, 0.1);
}

.dark .chat-message.gift .message-content {
  background-color: rgba(255, 51, 102, 0.2);
}

.chat-message.gift .message-content p {
  color: #FF3366;
  font-weight: 600;
}

.chat-input {
  display: flex;
  padding: 15px;
  border-top: 1px solid var(--light-gray);
}

.dark .chat-input {
  border-top-color: #2a2a2a;
}

.chat-input input {
  flex: 1;
  padding: 10px 15px;
  border: 1px solid var(--light-gray);
  border-radius: 20px;
  font-size: 0.95rem;
  background-color: var(--gray);
  color: var(--text-color);
}

.dark .chat-input input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.chat-input input:focus {
  outline: none;
  border-color: #6C13B3;
}

.dark .chat-input input:focus {
  border-color: #9B4BDE;
}

.send-btn {
  margin-left: 10px;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
}

/* For mobile devices */
@media (max-width: 768px) {
  .stream-content {
    flex-direction: column;
  }

  .stream-player {
    padding: 10px;
  }

  .stream-chat {
    width: 100%;
    height: 350px;
    border-left: none;
    border-top: 1px solid var(--light-gray);
  }

  .dark .stream-chat {
    border-top-color: #2a2a2a;
  }

  .video-overlay {
    opacity: 1;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7) 0%, transparent 30%, transparent 70%, rgba(0, 0, 0, 0.7) 100%);
  }

  .volume-slider {
    display: none;
  }

  .stream-info-bar {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .stream-actions {
    width: 100%;
    justify-content: space-between;
  }

  .interaction-bar {
    flex-wrap: wrap;
  }

  .interaction-btn {
    min-width: 100px;
  }
}
