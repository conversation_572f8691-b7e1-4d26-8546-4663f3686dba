import React, { useState, useRef, useEffect } from 'react';
import { 
  FiPlay, FiPause, FiVolume2, FiVolumeX, FiMaximize, FiMinimize, 
  FiSettings, FiSkipForward, FiSkipBack, FiRotateCw, FiDownload, 
  FiChevronsRight, FiChevronsLeft
} from 'react-icons/fi';
import './VideoPlayer.css';

const VideoPlayer = ({ 
  src, 
  poster, 
  autoPlay = false, 
  loop = true, 
  muted = false,
  onPlay,
  onPause,
  onEnded,
  onTimeUpdate,
  onProgress,
  className = '',
  controls = true,
  allowDownload = false,
  allowPictureInPicture = true,
  playbackRates = [0.5, 0.75, 1, 1.25, 1.5, 2],
  thumbnails = []
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(muted);
  const [volume, setVolume] = useState(1);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [showThumbnail, setShowThumbnail] = useState(false);
  const [thumbnailPosition, setThumbnailPosition] = useState(0);
  const [thumbnailTime, setThumbnailTime] = useState(0);
  const [isBuffering, setIsBuffering] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  
  const videoRef = useRef(null);
  const containerRef = useRef(null);
  const controlsTimeoutRef = useRef(null);
  const progressBarRef = useRef(null);
  const volumeSliderRef = useRef(null);
  const settingsRef = useRef(null);
  
  // Initialize player
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;
    
    // Set initial volume
    videoElement.volume = volume;
    videoElement.muted = isMuted;
    
    // Set initial playback rate
    videoElement.playbackRate = playbackRate;
    
    // Handle autoplay
    if (autoPlay) {
      videoElement.play()
        .then(() => setIsPlaying(true))
        .catch(error => console.error('Autoplay prevented:', error));
    }
    
    // Add event listeners for buffering detection
    const handleWaiting = () => setIsBuffering(true);
    const handlePlaying = () => setIsBuffering(false);
    
    videoElement.addEventListener('waiting', handleWaiting);
    videoElement.addEventListener('playing', handlePlaying);
    
    return () => {
      videoElement.removeEventListener('waiting', handleWaiting);
      videoElement.removeEventListener('playing', handlePlaying);
    };
  }, [autoPlay, isMuted, volume, playbackRate]);
  
  // Handle fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(
        document.fullscreenElement === containerRef.current ||
        document.webkitFullscreenElement === containerRef.current ||
        document.mozFullScreenElement === containerRef.current ||
        document.msFullscreenElement === containerRef.current
      );
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);
  
  // Handle click outside settings menu
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (settingsRef.current && !settingsRef.current.contains(event.target)) {
        setShowSettings(false);
      }
      
      if (volumeSliderRef.current && !volumeSliderRef.current.contains(event.target)) {
        setShowVolumeSlider(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  // Auto-hide controls after inactivity
  useEffect(() => {
    if (!showControls) return;
    
    const hideControls = () => setShowControls(false);
    
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    
    if (isPlaying) {
      controlsTimeoutRef.current = setTimeout(hideControls, 3000);
    }
    
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls, isPlaying]);
  
  const handleContainerMouseMove = () => {
    setShowControls(true);
    
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    
    if (isPlaying) {
      controlsTimeoutRef.current = setTimeout(() => setShowControls(false), 3000);
    }
  };
  
  const handleContainerMouseLeave = () => {
    if (isPlaying && !showSettings) {
      setShowControls(false);
    }
  };
  
  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;
    
    if (isPlaying) {
      video.pause();
      setIsPlaying(false);
      if (onPause) onPause();
    } else {
      video.play()
        .then(() => {
          setIsPlaying(true);
          if (onPlay) onPlay();
        })
        .catch(error => console.error('Play prevented:', error));
    }
  };
  
  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;
    
    const newMutedState = !isMuted;
    video.muted = newMutedState;
    setIsMuted(newMutedState);
  };
  
  const handleVolumeChange = (e) => {
    const video = videoRef.current;
    if (!video) return;
    
    const newVolume = parseFloat(e.target.value);
    video.volume = newVolume;
    setVolume(newVolume);
    
    if (newVolume === 0) {
      video.muted = true;
      setIsMuted(true);
    } else if (isMuted) {
      video.muted = false;
      setIsMuted(false);
    }
  };
  
  const handleTimeUpdate = () => {
    const video = videoRef.current;
    if (!video) return;
    
    const currentTime = video.currentTime;
    const duration = video.duration || 0;
    
    setCurrentTime(currentTime);
    setProgress((currentTime / duration) * 100);
    
    if (onTimeUpdate) {
      onTimeUpdate({
        currentTime,
        duration,
        progress: (currentTime / duration) * 100
      });
    }
  };
  
  const handleLoadedMetadata = () => {
    const video = videoRef.current;
    if (!video) return;
    
    setDuration(video.duration);
  };
  
  const handleProgressClick = (e) => {
    const video = videoRef.current;
    if (!video) return;
    
    const progressBar = progressBarRef.current;
    const rect = progressBar.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / rect.width;
    const newTime = pos * video.duration;
    
    video.currentTime = newTime;
    setCurrentTime(newTime);
    setProgress((newTime / video.duration) * 100);
  };
  
  const handleProgressMouseMove = (e) => {
    if (!thumbnails.length) return;
    
    const progressBar = progressBarRef.current;
    const rect = progressBar.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / rect.width;
    
    setShowThumbnail(true);
    setThumbnailPosition(e.clientX - rect.left);
    
    // Calculate time at position
    const timeAtPosition = pos * duration;
    setThumbnailTime(timeAtPosition);
  };
  
  const handleProgressMouseLeave = () => {
    setShowThumbnail(false);
  };
  
  const toggleFullscreen = () => {
    if (!containerRef.current) return;
    
    if (!isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      } else if (containerRef.current.webkitRequestFullscreen) {
        containerRef.current.webkitRequestFullscreen();
      } else if (containerRef.current.mozRequestFullScreen) {
        containerRef.current.mozRequestFullScreen();
      } else if (containerRef.current.msRequestFullscreen) {
        containerRef.current.msRequestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
  };
  
  const handlePlaybackRateChange = (rate) => {
    const video = videoRef.current;
    if (!video) return;
    
    video.playbackRate = rate;
    setPlaybackRate(rate);
    setShowSettings(false);
  };
  
  const skipForward = () => {
    const video = videoRef.current;
    if (!video) return;
    
    video.currentTime = Math.min(video.duration, video.currentTime + 10);
  };
  
  const skipBackward = () => {
    const video = videoRef.current;
    if (!video) return;
    
    video.currentTime = Math.max(0, video.currentTime - 10);
  };
  
  const handleDownload = () => {
    if (!src) return;
    
    const a = document.createElement('a');
    a.href = src;
    a.download = src.split('/').pop() || 'video';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };
  
  const togglePictureInPicture = async () => {
    const video = videoRef.current;
    if (!video) return;
    
    try {
      if (document.pictureInPictureElement) {
        await document.exitPictureInPicture();
      } else {
        await video.requestPictureInPicture();
      }
    } catch (error) {
      console.error('Picture-in-Picture failed:', error);
    }
  };
  
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };
  
  // Find thumbnail for current time
  const getCurrentThumbnail = () => {
    if (!thumbnails.length) return null;
    
    // Find the thumbnail that corresponds to the current time
    for (let i = thumbnails.length - 1; i >= 0; i--) {
      if (thumbnailTime >= thumbnails[i].time) {
        return thumbnails[i].url;
      }
    }
    
    return thumbnails[0].url;
  };
  
  return (
    <div 
      className={`video-player-container ${className} ${isFullscreen ? 'fullscreen' : ''}`}
      ref={containerRef}
      onMouseMove={handleContainerMouseMove}
      onMouseLeave={handleContainerMouseLeave}
    >
      <video
        ref={videoRef}
        src={src}
        poster={poster}
        className="video-player-element"
        loop={loop}
        muted={isMuted}
        onClick={togglePlay}
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={handleLoadedMetadata}
        onEnded={() => {
          setIsPlaying(false);
          if (onEnded) onEnded();
        }}
      />
      
      {isBuffering && (
        <div className="buffering-indicator">
          <div className="spinner"></div>
        </div>
      )}
      
      {!isPlaying && (
        <div className="play-overlay" onClick={togglePlay}>
          <button className="play-button">
            <FiPlay />
          </button>
        </div>
      )}
      
      {controls && (showControls || !isPlaying) && (
        <div className="video-controls-container">
          {/* Progress bar */}
          <div 
            className="progress-container" 
            ref={progressBarRef}
            onClick={handleProgressClick}
            onMouseMove={handleProgressMouseMove}
            onMouseLeave={handleProgressMouseLeave}
          >
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            
            {showThumbnail && thumbnails.length > 0 && (
              <div 
                className="thumbnail-preview"
                style={{ 
                  left: `${thumbnailPosition}px`,
                  backgroundImage: `url(${getCurrentThumbnail()})`
                }}
              >
                <div className="thumbnail-time">{formatTime(thumbnailTime)}</div>
              </div>
            )}
          </div>
          
          <div className="controls-row">
            <div className="left-controls">
              <button className="control-button" onClick={togglePlay}>
                {isPlaying ? <FiPause /> : <FiPlay />}
              </button>
              
              <button className="control-button" onClick={skipBackward}>
                <FiChevronsLeft />
                <span className="control-tooltip">-10s</span>
              </button>
              
              <button className="control-button" onClick={skipForward}>
                <FiChevronsRight />
                <span className="control-tooltip">+10s</span>
              </button>
              
              <div 
                className="volume-container"
                ref={volumeSliderRef}
                onMouseEnter={() => setShowVolumeSlider(true)}
              >
                <button className="control-button" onClick={toggleMute}>
                  {isMuted || volume === 0 ? <FiVolumeX /> : <FiVolume2 />}
                </button>
                
                {showVolumeSlider && (
                  <div className="volume-slider-container">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.01"
                      value={isMuted ? 0 : volume}
                      onChange={handleVolumeChange}
                      className="volume-slider"
                    />
                  </div>
                )}
              </div>
              
              <div className="time-display">
                <span>{formatTime(currentTime)}</span>
                <span>/</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>
            
            <div className="right-controls">
              {allowPictureInPicture && (
                <button className="control-button" onClick={togglePictureInPicture}>
                  <FiMinimize />
                  <span className="control-tooltip">Picture-in-Picture</span>
                </button>
              )}
              
              {allowDownload && (
                <button className="control-button" onClick={handleDownload}>
                  <FiDownload />
                  <span className="control-tooltip">Download</span>
                </button>
              )}
              
              <div className="settings-container" ref={settingsRef}>
                <button 
                  className="control-button" 
                  onClick={() => setShowSettings(!showSettings)}
                >
                  <FiSettings />
                </button>
                
                {showSettings && (
                  <div className="settings-menu">
                    <div className="settings-section">
                      <h4>Playback Speed</h4>
                      <div className="playback-rates">
                        {playbackRates.map(rate => (
                          <button
                            key={rate}
                            className={`playback-rate-btn ${playbackRate === rate ? 'active' : ''}`}
                            onClick={() => handlePlaybackRateChange(rate)}
                          >
                            {rate === 1 ? 'Normal' : `${rate}x`}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              <button className="control-button" onClick={toggleFullscreen}>
                {isFullscreen ? <FiMinimize /> : <FiMaximize />}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;
