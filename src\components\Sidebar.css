.sidebar {
  width: 300px;
  height: 100%;
  border-right: 1px solid var(--light-gray);
  padding: 20px 0;
  overflow-y: auto;
  transition: background-color 0.3s, color 0.3s;
}

.sidebar.dark {
  border-right-color: #2a2a2a;
}

.sidebar-menu {
  margin-bottom: 20px;
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 20px;
  font-size: 1rem;
  color: var(--text-color);
  transition: background-color 0.2s, color 0.2s;
  text-decoration: none;
}

.dark .sidebar-item {
  color: var(--white);
}

.sidebar-item:hover {
  background-color: var(--gray);
}

.dark .sidebar-item:hover {
  background-color: #2a2a2a;
}

.sidebar-item.active {
  color: #6C13B3;
}

.dark .sidebar-item.active {
  color: #9B4BDE;
}

.sidebar-section {
  padding: 10px 20px;
  margin-bottom: 20px;
}

.sidebar-section h3 {
  font-size: 0.9rem;
  color: var(--dark-gray);
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.dark .sidebar-section h3 {
  color: #aaa;
}

.account-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.account-item {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  color: var(--text-color);
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.dark .account-item {
  color: var(--white);
}

.account-item:hover {
  background-color: var(--gray);
}

.dark .account-item:hover {
  background-color: #2a2a2a;
}

.account-item img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.account-name-container {
  display: flex;
  align-items: center;
  gap: 5px;
}

.name {
  font-weight: 600;
  font-size: 0.9rem;
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.7rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.username {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .username {
  color: #aaa;
}

.see-more-btn {
  margin-top: 10px;
  color: #6C13B3;
  font-size: 0.9rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.dark .see-more-btn {
  color: #9B4BDE;
}

.see-more-btn:hover {
  background-color: rgba(108, 19, 179, 0.1);
}

.dark .see-more-btn:hover {
  background-color: rgba(155, 75, 222, 0.2);
}

.hashtag-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.hashtag-item {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  color: var(--text-color);
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.dark .hashtag-item {
  color: var(--white);
}

.hashtag-item:hover {
  background-color: var(--gray);
}

.dark .hashtag-item:hover {
  background-color: #2a2a2a;
}

.hashtag-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(108, 19, 179, 0.1);
  color: #6C13B3;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark .hashtag-icon {
  background-color: rgba(155, 75, 222, 0.2);
  color: #9B4BDE;
}

.hashtag-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.hashtag-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.hashtag-count {
  font-size: 0.8rem;
  color: var(--dark-gray);
}

.dark .hashtag-count {
  color: #aaa;
}

.sidebar-footer {
  padding: 20px;
  font-size: 0.8rem;
  color: var(--dark-gray);
  border-top: 1px solid var(--light-gray);
  margin-top: 20px;
}

.dark .sidebar-footer {
  color: #aaa;
  border-top-color: #2a2a2a;
}

.footer-links {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.footer-links a {
  color: var(--dark-gray);
  text-decoration: none;
}

.dark .footer-links a {
  color: #aaa;
}

.footer-links a:hover {
  text-decoration: underline;
}

/* For mobile devices */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid var(--light-gray);
    padding: 10px 0;
  }

  .dark .sidebar {
    border-bottom-color: #2a2a2a;
  }

  .sidebar-menu {
    display: flex;
    justify-content: space-around;
    margin-bottom: 0;
  }

  .sidebar-item {
    flex-direction: column;
    padding: 10px;
    font-size: 0.8rem;
  }

  .sidebar-section, .sidebar-footer {
    display: none;
  }
}
