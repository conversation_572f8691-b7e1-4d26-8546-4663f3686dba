.explore-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  position: relative;
}

.explore-header {
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
}

.explore-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.search-container {
  position: relative;
  max-width: 600px;
  z-index: 10;
}

.explore-search {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--dark-gray);
}

.dark .search-icon {
  color: #aaa;
}

.explore-search input {
  width: 100%;
  padding: 15px 15px 15px 45px;
  border-radius: 30px;
  border: 1px solid var(--light-gray);
  font-size: 1rem;
  background-color: var(--gray);
  transition: all 0.2s;
}

.dark .explore-search input {
  background-color: #2a2a2a;
  border-color: #3a3a3a;
  color: var(--white);
}

.explore-search input:focus {
  outline: none;
  border-color: #6C13B3;
  box-shadow: 0 0 0 2px rgba(108, 19, 179, 0.2);
}

.dark .explore-search input:focus {
  border-color: #9B4BDE;
  box-shadow: 0 0 0 2px rgba(155, 75, 222, 0.3);
}

.search-btn {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  /* Other styles are handled by global button classes */
}

.explore-categories-wrapper {
  margin-bottom: 30px;
}

.explore-categories {
  display: flex;
  gap: 15px;
  padding: 5px 0;
}

.category-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  min-width: 80px;
  /* Other styles are handled by global button classes */
}

.category-btn svg {
  font-size: 1.5rem;
}

.explore-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.explore-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.recommendation-info {
  font-size: 0.85rem;
  color: var(--dark-gray);
  background-color: var(--gray);
  padding: 5px 10px;
  border-radius: 20px;
}

.dark .recommendation-info {
  color: #aaa;
  background-color: #2a2a2a;
}

.section-header h2 {
  font-size: 1.3rem;
  font-weight: 700;
}

.see-all-link {
  color: #6C13B3;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s;
}

.dark .see-all-link {
  color: #9B4BDE;
}

.see-all-link:hover {
  color: #5a0e9c;
  text-decoration: underline;
}

.dark .see-all-link:hover {
  color: #8a3dcf;
}

/* Hashtags Grid */
.hashtags-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
}

.hashtag-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  text-decoration: none;
  color: var(--text-color);
  transition: transform 0.2s;
}

.dark .hashtag-card {
  color: var(--white);
}

.hashtag-card:hover {
  transform: translateY(-5px);
}

.hashtag-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.hashtag-info {
  text-align: center;
}

.hashtag-info h3 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 5px;
}

.hashtag-info p {
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.dark .hashtag-info p {
  color: #aaa;
}

/* Videos Grid */
.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.video-card {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  text-decoration: none;
  color: var(--text-color);
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.dark .video-card {
  background-color: #1e1e1e;
  color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.video-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark .video-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.video-thumbnail {
  position: relative;
  aspect-ratio: 3/4;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-views {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--white);
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.video-info {
  padding: 15px;
}

.video-info h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 10px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-creator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-creator img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}

.video-creator span {
  font-size: 0.85rem;
  color: var(--dark-gray);
}

.dark .video-creator span {
  color: #aaa;
}

/* Creators Grid */
.creators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.creator-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-decoration: none;
  color: var(--text-color);
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
}

.dark .creator-card {
  background-color: #1e1e1e;
  color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.creator-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark .creator-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.creator-avatar {
  margin-bottom: 15px;
}

.creator-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #6C13B3;
}

.dark .creator-avatar img {
  border-color: #9B4BDE;
}

.creator-info {
  text-align: center;
  margin-bottom: 15px;
}

.creator-info h3 {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.verified-badge {
  color: #6C13B3;
  font-size: 0.8rem;
}

.dark .verified-badge {
  color: #9B4BDE;
}

.creator-info p {
  font-size: 0.9rem;
  color: var(--dark-gray);
  margin-bottom: 5px;
}

.dark .creator-info p {
  color: #aaa;
}

.creator-followers {
  font-size: 0.85rem;
  color: var(--dark-gray);
}

.dark .creator-followers {
  color: #aaa;
}

/* Follow button styles are now handled by global button classes */

/* Recent Searches Dropdown */
.recent-searches {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-top: 5px;
  z-index: 10;
  overflow: hidden;
}

.dark .recent-searches {
  background-color: #1e1e1e;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.recent-searches-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--light-gray);
}

.dark .recent-searches-header {
  border-bottom-color: #2a2a2a;
}

.recent-searches-header h3 {
  font-size: 1rem;
  font-weight: 600;
}

.clear-btn {
  background: none;
  border: none;
  color: #6C13B3;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
}

.dark .clear-btn {
  color: #9B4BDE;
}

.recent-search-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recent-search-list li {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.recent-search-list li:hover {
  background-color: var(--gray);
}

.dark .recent-search-list li:hover {
  background-color: #2a2a2a;
}

/* Filter Dropdown */
.filter-container {
  position: relative;
  z-index: 5;
}

.filter-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  border-radius: 20px;
  background-color: var(--gray);
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.dark .filter-toggle-btn {
  background-color: #2a2a2a;
  color: var(--white);
}

.filter-toggle-btn.active {
  background-color: #6C13B3;
  color: white;
}

.dark .filter-toggle-btn.active {
  background-color: #9B4BDE;
}

.filter-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 250px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-top: 10px;
  padding: 15px;
  z-index: 10;
}

.dark .filter-dropdown {
  background-color: #1e1e1e;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.filter-dropdown h3 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.filter-option label {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
}

.clear-filters-btn, .apply-filters-btn {
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.clear-filters-btn {
  background: none;
  border: 1px solid var(--light-gray);
  color: var(--text-color);
}

.dark .clear-filters-btn {
  border-color: #2a2a2a;
  color: var(--white);
}

.apply-filters-btn {
  background-color: #6C13B3;
  border: none;
  color: white;
}

.dark .apply-filters-btn {
  background-color: #9B4BDE;
}

/* Scrollable Containers */
.hashtags-scroll,
.creators-scroll {
  margin-bottom: 20px;
}

.hashtags-scroll .hashtags-grid,
.creators-scroll .creators-grid {
  flex-wrap: nowrap;
  display: flex;
  gap: 15px;
}

.hashtags-scroll .hashtag-card,
.creators-scroll .creator-card {
  min-width: 200px;
  flex: 0 0 auto;
}

/* Recommended Videos */
.video-card.recommended {
  border: 2px solid #6C13B3;
}

.dark .video-card.recommended {
  border-color: #9B4BDE;
}

.recommended-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: #6C13B3;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
}

.dark .recommended-badge {
  background-color: #9B4BDE;
}

/* Category Discovery Grid */
.category-discovery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.category-discovery-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 25px 15px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  text-decoration: none;
  color: var(--text-color);
  transition: transform 0.2s, box-shadow 0.2s;
}

.dark .category-discovery-card {
  background-color: #1e1e1e;
  color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.category-discovery-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark .category-discovery-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.category-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: rgba(108, 19, 179, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 1.8rem;
  color: #6C13B3;
}

.dark .category-icon {
  background-color: rgba(155, 75, 222, 0.2);
  color: #9B4BDE;
}

.category-discovery-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.category-discovery-card p {
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.dark .category-discovery-card p {
  color: #aaa;
}

/* Search Overlay for Mobile */
.explore-search-overlay {
  display: none;
  position: fixed;
  bottom: 80px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--white);
  padding: 10px 20px;
  border-radius: 30px;
  z-index: 100;
  cursor: pointer;
}

.search-prompt {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Hashtags detailed page styles */
.back-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-color);
  cursor: pointer;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.dark .back-btn {
  color: var(--white);
}

.back-btn:hover {
  background-color: var(--gray);
}

.dark .back-btn:hover {
  background-color: #2a2a2a;
}

.filter-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  overflow-x: auto;
  padding: 5px 0;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  border-radius: 20px;
  background-color: var(--gray);
  color: var(--text-color);
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.dark .filter-tab {
  background-color: #2a2a2a;
  color: var(--white);
}

.filter-tab.active {
  background-color: #6C13B3;
  color: white;
}

.dark .filter-tab.active {
  background-color: #9B4BDE;
}

.hashtags-detailed-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.hashtag-detailed-card {
  display: flex;
  flex-direction: column;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  transition: all 0.3s ease;
  background-color: var(--white);
}

.dark .hashtag-detailed-card {
  background-color: #1e1e1e;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.hashtag-detailed-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dark .hashtag-detailed-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.hashtag-header {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.hashtag-header h2 {
  color: white;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.hashtag-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.hashtag-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 10px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-value {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-color);
}

.dark .stat-value {
  color: var(--white);
}

.stat-label {
  font-size: 0.9rem;
  color: var(--dark-gray);
}

.dark .stat-label {
  color: #aaa;
}

.hashtag-description {
  font-size: 0.95rem;
  color: var(--text-color);
  line-height: 1.5;
  margin: 0;
}

.dark .hashtag-description {
  color: var(--white);
}

.hashtag-preview {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.preview-thumbnail {
  width: calc(33.333% - 7px);
  aspect-ratio: 9/16;
  border-radius: 8px;
  overflow: hidden;
}

.preview-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.view-hashtag-btn {
  padding: 12px;
  border-radius: 8px;
  border: none;
  color: white;
  font-weight: 600;
  cursor: pointer;
  margin-top: 10px;
  transition: all 0.3s ease;
}

.view-hashtag-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

/* For mobile devices */
@media (max-width: 768px) {
  .explore-container {
    padding: 15px;
  }

  .explore-header {
    flex-direction: column;
  }

  .explore-header h1 {
    font-size: 1.5rem;
  }

  .search-container {
    max-width: 100%;
    width: 100%;
  }

  .filter-container {
    align-self: flex-end;
    margin-top: 10px;
  }

  .category-btn {
    min-width: 80px;
  }

  .hashtags-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .videos-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .creators-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .category-discovery-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .creator-avatar img {
    width: 60px;
    height: 60px;
  }

  .filter-dropdown {
    right: -10px;
  }

  .section-actions {
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
  }

  .recommendation-info {
    font-size: 0.75rem;
  }

  .recent-searches {
    width: 100%;
  }

  /* Scrollable containers on mobile */
  .scroll-arrow {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .scroll-left {
    left: -5px;
  }

  .scroll-right {
    right: -5px;
  }

  .hashtags-scroll .hashtag-card,
  .creators-scroll .creator-card {
    min-width: 160px;
  }

  /* Hide section navigation on mobile */
  .section-navigation {
    display: none;
  }

  /* Adjust back to top button position */
  .back-to-top-button {
    bottom: 80px;
    right: 20px;
  }
}

@media (max-width: 480px) {
  .hashtags-grid,
  .videos-grid,
  .creators-grid,
  .category-discovery-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .explore-header h1 {
    font-size: 1.5rem;
  }

  .section-header h2 {
    font-size: 1.1rem;
  }

  .explore-search input {
    padding: 12px 12px 12px 40px;
    font-size: 0.9rem;
  }

  .search-btn {
    padding: 6px 10px;
    font-size: 0.8rem;
  }

  .category-btn {
    padding: 10px;
    min-width: 70px;
  }

  .category-btn svg {
    font-size: 1.2rem;
  }

  .category-btn span {
    font-size: 0.8rem;
  }
}
