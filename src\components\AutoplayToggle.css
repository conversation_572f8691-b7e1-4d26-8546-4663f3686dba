.autoplay-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
}

.autoplay-label {
  font-weight: 500;
  color: var(--text-color);
}

.dark .autoplay-label {
  color: var(--white);
}

/* Size variants */
.size-small .autoplay-label {
  font-size: 0.8rem;
}

.size-medium .autoplay-label {
  font-size: 0.9rem;
}

.size-large .autoplay-label {
  font-size: 1rem;
}

.toggle-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.toggle-track {
  position: relative;
  width: 46px;
  height: 24px;
  border-radius: 12px;
  background-color: var(--light-gray);
  transition: background-color 0.3s ease;
}

.dark .toggle-track {
  background-color: #2a2a2a;
}

.toggle-button.enabled .toggle-track {
  background-color: #6C13B3;
}

.dark .toggle-button.enabled .toggle-track {
  background-color: #9B4BDE;
}

.toggle-indicator {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.toggle-button.enabled .toggle-indicator {
  transform: translateX(22px);
}

.toggle-icon {
  font-size: 0.7rem;
  color: #6C13B3;
  opacity: 0;
  transition: opacity 0.2s;
}

.dark .toggle-icon {
  color: #9B4BDE;
}

.toggle-button.enabled .toggle-icon {
  opacity: 1;
}

.toggle-status {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--dark-gray);
}

.dark .toggle-status {
  color: #aaa;
}

.toggle-button.enabled .toggle-status {
  color: #6C13B3;
}

.dark .toggle-button.enabled .toggle-status {
  color: #9B4BDE;
}

/* Size variants for toggle */
.size-small .toggle-track {
  width: 36px;
  height: 18px;
}

.size-small .toggle-indicator {
  width: 14px;
  height: 14px;
}

.size-small .toggle-button.enabled .toggle-indicator {
  transform: translateX(18px);
}

.size-large .toggle-track {
  width: 56px;
  height: 30px;
}

.size-large .toggle-indicator {
  width: 26px;
  height: 26px;
}

.size-large .toggle-button.enabled .toggle-indicator {
  transform: translateX(26px);
}

/* Animation */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(108, 19, 179, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(108, 19, 179, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(108, 19, 179, 0);
  }
}

.toggle-button.enabled .toggle-indicator {
  animation: pulse 1.5s infinite;
}

.dark .toggle-button.enabled .toggle-indicator {
  animation: pulse 1.5s infinite;
}
