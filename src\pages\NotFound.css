.not-found-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 70vh;
  text-align: center;
  padding: 20px;
}

.not-found-container h1 {
  font-size: 6rem;
  color: var(--primary-color);
  margin-bottom: 10px;
}

.not-found-container h2 {
  font-size: 2rem;
  margin-bottom: 20px;
}

.not-found-container p {
  color: var(--dark-gray);
  margin-bottom: 30px;
  max-width: 500px;
}

.home-link {
  padding: 12px 24px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 4px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.home-link:hover {
  background-color: #e6254e;
}
